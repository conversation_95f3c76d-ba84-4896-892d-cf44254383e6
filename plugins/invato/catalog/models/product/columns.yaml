columns:
    title:
        label: 'invato.catalog::lang.fields.title'
        type: text
        searchable: true
        sortable: true
    slug:
        label: 'invato.catalog::lang.fields.slug'
        type: text
        invisible: true
        searchable: true
        sortable: true
    price:
        label: 'invato.catalog::lang.fields.price'
        type: number
        sortable: true
    custom_options:
        label: 'invato.catalog::lang.fields.custom_options'
        type: text
        invisible: true
        searchable: true
    created_at:
        label: 'Created at'
        type: date
        sortable: true
        format: d-m-Y
    updated_at:
        label: 'Updated at'
        type: date
        sortable: true
        format: d-m-Y
    # Deleted at only visible when filtering, with trashed is enabled
    deleted_at:
        label: 'Deleted at'
        type: datetime
        invisible: true
    actions:
        label: 'Actions'
        type: partial
        path: column_actions
        clickable: false
        sortable: false
        align: center
        cssClass: 'column-button'
        width: '110px'
