columns:
    title:
        label: 'invato.catalog::lang.fields.title'
        type: text
        searchable: true
        sortable: false
        indent: true  # This enables the tree indentation
    slug:
        label: 'invato.catalog::lang.fields.slug'
        type: text
        searchable: true
        sortable: false
    created_at:
        label: 'Created at'
        type: date
        sortable: false
        format: d-m-Y
    updated_at:
        label: 'Updated at'
        type: date
        sortable: false
        format: d-m-Y
    # Deleted at only visible when filtering, with trashed is enabled
    deleted_at:
        label: 'Deleted at'
        type: datetime
        invisible: true
    actions:
        label: 'Actions'
        type: partial
        path: column_actions
        clickable: false
        sortable: false
        align: center
        cssClass: 'column-button'
        width: '110px'
