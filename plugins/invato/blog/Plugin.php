<?php

namespace Invato\Blog;

use App\Console\Commands\BlogSyncFromApiEndpointCommand;
use Event;
use Illuminate\Support\Facades\Route;
use Invato\Blog\classes\BlogSyncController;
use Invato\Blog\Models\BlogSetting;
use Invato\Blog\Models\Category;
use Invato\Blog\Models\Post;
use Invato\Traits\RegistersPageFinderTrait;
use Invato\Traits\SettingsMenuContextTrait;
use OFFLINE\Boxes\Models\Box;
use System\Classes\PluginBase;

/**
 * Plugin class
 */
class Plugin extends PluginBase
{
    use RegistersPageFinderTrait;
    use SettingsMenuContextTrait;

    public function boot(): void
    {
        $this->setupSettingsMenuContext('settings');
        $this->registerPageFinder();

        Route::get('api/blog/sync/{resource}', [BlogSyncController::class, 'apiEndpoint']);

        Box::extend(function ($model) {
            $model->addDynamicMethod('getBlogCategoryOptions', function ($value) use ($model) {
                return Category::get()->lists('title', 'slug');
            });
        });
    }

    public function register()
    {
        $this->registerConsoleCommand('invato.blog.sync', BlogSyncFromApiEndpointCommand::class);
    }

    public function registerSchedule($schedule)
    {
        $blogSettings = BlogSetting::instance();

        if (isset($blogSettings['syncWithDomain'], $blogSettings['authToken'])) {
            $schedule->command('invato:blog:sync-from-api-endpoint')
                ->timezone('Europe/Amsterdam')
                ->daily();
        }
    }

    /**
     * registerComponents used by the frontend.
     */
    public function registerComponents()
    {
        return [
            'Invato\Blog\Components\PostList' => 'postList',
            'Invato\Blog\Components\CategoryList' => 'categoryList',
            'Invato\Blog\Components\PostDetail' => 'postDetail',
            'Invato\Blog\Components\CategoryDetail' => 'categoryDetail',
        ];
    }

    public function registerSnippets()
    {
        return [
            'Invato\Blog\Components\PostList' => 'postList',
            'Invato\Blog\Components\CategoryList' => 'categoryList',
            'Invato\Blog\Components\PostDetail' => 'postDetail',
            'Invato\Blog\Components\CategoryDetail' => 'categoryDetail',
        ];
    }

    /**
     * registerSettings used by the backend.
     */
    public function registerSettings()
    {
        return [
            'settings' => [
                'label' => 'invato.blog::lang.settings.blogsettings',
                'description' => 'invato.blog::lang.settings.blogsettingsdescription',
                'category' => 'Plugins',
                'icon' => 'icon-cog',
                'class' => BlogSetting::class,
            ],
        ];
    }

    protected function registerPageFinder(): void
    {
        $listTypes = function () {
            return [
                'blog-post' => 'Blog Post',
                'all-blog-posts' => 'All Blog Posts',
                'blog-category' => 'Blog Category',
                'all-blog-categories' => 'All Blog Categories',
            ];
        };

        $getTypeInfo = function ($type) {
            if ($type == 'blog-post' || $type == 'all-blog-posts') {
                return Post::getMenuTypeInfo($type);
            }
            if ($type == 'blog-category' || $type == 'all-blog-categories') {
                return Category::getMenuTypeInfo($type);
            }

            return [];
        };

        $resolveItem = function ($type, $item, $url, $theme) {
            if ($type == 'blog-post' || $type == 'all-blog-posts') {
                return Post::resolveMenuItem($item, $url, $theme);
            }
            if ($type == 'blog-category' || $type == 'all-blog-categories') {
                return Category::resolveMenuItem($item, $url, $theme);
            }

            return null;
        };

        Event::listen([
            'cms.pageLookup.listTypes',
            'pages.menuitem.listTypes',
        ], $listTypes);

        Event::listen([
            'cms.pageLookup.getTypeInfo',
            'pages.menuitem.getTypeInfo',
        ], $getTypeInfo);

        Event::listen([
            'cms.pageLookup.resolveItem',
            'pages.menuitem.resolveItem',
        ], $resolveItem);

    }

    /**
     * Get PageFinder configuration for this plugin
     */
    protected function getPageFinderConfig(): array
    {
        return [
            'model' => Post::class,
            'menu_types' => [
                'blog-post' => 'invato.blog::lang.menuitem.blog_post',
                'all-blog-posts' => 'invato.blog::lang.menuitem.all_blog_posts',
            ],
        ];
    }
}
