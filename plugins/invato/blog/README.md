# Interne documentatie

## Doel:

Het beheren en tonen van blog posts, categorieën en auteurs voor de website, met mogelijkheden voor SEO optimalisatie en content management.

## Backend:

De plugin heeft 4 backend pagina's, die in het linkermenu onder kopje Blog vallen: Categorieën, <PERSON>s, Auteurs en Instellingen.

### Blog instellingen pagina's:

* #### Categorieën
  Hier kunnen de verschillende blog categorieën aangemaakt worden. Deze hebben verschillende opties die afhankelijk van het design op de front-end weergegeven kunnen worden. Te<PERSON>s beschikt deze ook over SEO opties.
* #### Posts
  Hier kunnen de blog posts opgevoerd worden met volledige content management mogelijkheden.
* #### Auteurs
  Hier kunnen de blog auteurs beheerd worden met hun profielinformatie.
* #### Instellingen
  Hier staan de opties voor de plugin, zoals de pagina die als overzicht of posts worden gebruikt.

### Wie heeft toegang?

* Invato
* <PERSON><PERSON> met toegang tot de plugin.

### Instellingen

Hier staan de opties voor de plugin, zoals de pagina die als overzicht of posts worden gebruikt.

### Wie heeft toegang?

* Invato

## Front-end:

Deze plugin bevat componenten die als overzichtpagina te gebruiken zijn. Verder heeft deze plugin CMS pagina's voor statische inhoud: categorie detail pagina en post detail pagina.

### Wie heeft toegang?

* Invato
* Klant met toegang tot de plugin.

## Plugin uitbereiden (Extensie plugin)

De blog plugin is met een extensie plugin gemakkelijk uit te breiden met nieuwe functies, componenten en extra velden.

### Extra velden toevoegen

Om extra velden aan te maken, is er een speciaal JSON veld in de database aangemaakt, zodat er geen migrations uitgevoerd hoeven te worden. Dit is het 'additional_content' veld.

In de Plugin.php van de extensie plugin moet in de boot() method het 'extendFields' Event aangeroepen worden om de velden toe te voegen:

```php
Event::listen('backend.form.extendFields', function ($widget) {
    if (
        !$widget->model instanceof \Invato\Blog\Models\Post
    ) {
        return;
    }

    if ($widget->isNested) {
        return;
    }

    $widget->addFields([
        'additional_content[custom_field]' => [
            'label' => 'Custom Field',
        ],
    ]);
    $widget->addTabFields([
        'additional_content[featured]' => [
            'label' => 'Featured Post',
            'type' =>  'switch',
            'tab' => 'invato.blog::lang.tabs.content',
        ],
    ]);
});
```

## Soft Delete Functionaliteit

Alle content kan veilig verwijderd worden:
- Verwijderde items worden niet permanent gewist
- Gebruik de "Show deleted" filter om verwijderde items te zien
- Herstel items met de restore functie
- Permanent verwijderen is mogelijk via de backend

## Import/Export Functionaliteit

De plugin ondersteunt volledige import en export mogelijkheden:
- Posts kunnen geïmporteerd en geëxporteerd worden
- Categorieën kunnen geïmporteerd en geëxporteerd worden  
- Auteurs kunnen geïmporteerd en geëxporteerd worden
- Duplicate functionaliteit voor alle content types

## PageFinder Integratie

De plugin is volledig geïntegreerd met het PageFinder systeem:
- Automatische menu item generatie voor blog posts
- Ondersteuning voor single post en all posts menu types
- Automatische redirect handling bij verwijdering

## Gebruikte permissions:

- invato.blog.manage_blog
- invato.blog.manage_categories  
- invato.blog.manage_posts
- invato.blog.manage_authors
- invato.blog.import_posts
- invato.blog.export_posts
- invato.blog.import_categories
- invato.blog.export_categories
- invato.blog.import_authors
- invato.blog.export_authors
- superusers.view_readme
