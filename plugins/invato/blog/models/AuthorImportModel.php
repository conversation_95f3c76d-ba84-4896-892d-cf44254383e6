<?php

namespace Invato\Blog\Models;

use Backend\Models\ImportModel;
use Exception;

class AuthorImportModel extends ImportModel
{
    public $rules = [
        'first_name' => 'required|string|max:255',
        'last_name' => 'required|string|max:255',
    ];

    public function importData($results, $sessionKey = null): void
    {
        foreach ($results as $row => $data) {
            try {
                // Check if author with this name already exists
                $firstName = $data['first_name'] ?? '';
                $lastName = $data['last_name'] ?? '';
                
                $author = Author::withTrashed()
                    ->where('first_name', $firstName)
                    ->where('last_name', $lastName)
                    ->first();

                if (! $author) {
                    $author = new Author;
                }

                $author->fill([
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'bio' => $data['bio'] ?? null,
                    'email' => $data['email'] ?? null,
                    'phone_number' => $data['phone_number'] ?? null,
                ]);

                // Handle JSON arrays
                if (! empty($data['socials'])) {
                    if (is_string($data['socials'])) {
                        // Parse JSON if it's a string
                        $author->socials = json_decode($data['socials'], true, 512, JSON_THROW_ON_ERROR) ?: [];
                    } else {
                        $author->socials = $data['socials'];
                    }
                }

                $author->save();

                // Handle posts if provided
                if (! empty($data['posts'])) {
                    $postIds = [];
                    $posts = is_array($data['posts']) ? $data['posts'] : explode(',', $data['posts']);

                    foreach ($posts as $postTitle) {
                        $postTitle = trim($postTitle);
                        $post = Post::where('title', $postTitle)->first();

                        if ($post) {
                            $postIds[] = $post->id;
                        }
                    }

                    if (! empty($postIds)) {
                        // Update posts to have this author
                        Post::whereIn('id', $postIds)->update(['author_id' => $author->id]);
                    }
                }

                $this->logCreated();
            } catch (Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }
}
