<?php

namespace Invato\Blog\Models;

use Backend\Models\ExportModel;
use JsonException;

class PostExportModel extends ExportModel
{
    /**
     * @throws JsonException
     */
    public function exportData($columns, $sessionKey = null): array
    {
        // Simple test - just return basic post data
        $posts = Post::all();
        $exportData = [];

        foreach ($posts as $post) {
            $record = [];

            foreach ($columns as $column) {
                switch ($column) {
                    case 'categories':
                        $record[$column] = $post->categories ? $post->categories->pluck('title')->implode(', ') : '';
                        break;
                    case 'author':
                        if ($post->author) {
                            $record[$column] = trim($post->author->first_name . ' ' . $post->author->last_name);
                        } else {
                            $record[$column] = '';
                        }
                        break;
                    case 'additional_content':
                    case 'cta_button':
                        $value = $post->{$column};
                        $record[$column] = is_array($value) ? json_encode($value) : (string) $value;
                        break;
                    case 'is_featured':
                        $record[$column] = $post->{$column} ? '1' : '0';
                        break;
                    default:
                        $record[$column] = (string) $post->{$column};
                        break;
                }
            }

            $exportData[] = $record;
        }

        return $exportData;
    }
}
