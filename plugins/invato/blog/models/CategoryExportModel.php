<?php

namespace Invato\Blog\Models;

use Backend\Models\ExportModel;

class CategoryExportModel extends ExportModel
{
    public function exportData($columns, $sessionKey = null): array
    {
        $categories = Category::with(['posts', 'parent'])->get();
        $exportData = [];

        foreach ($categories as $category) {
            $record = [];
            
            foreach ($columns as $column) {
                switch ($column) {
                    case 'posts':
                        $record[$column] = $category->posts ? $category->posts->pluck('title')->implode(', ') : '';
                        break;
                    case 'parent':
                        $record[$column] = $category->parent ? $category->parent->title : '';
                        break;
                    case 'post_count':
                        $record[$column] = $category->posts ? $category->posts->count() : 0;
                        break;
                    default:
                        $record[$column] = (string) $category->{$column};
                        break;
                }
            }
            
            $exportData[] = $record;
        }

        return $exportData;
    }
}
