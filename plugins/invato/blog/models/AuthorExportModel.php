<?php

namespace Invato\Blog\Models;

use Backend\Models\ExportModel;

class AuthorExportModel extends ExportModel
{
    public function exportData($columns, $sessionKey = null): array
    {
        $authors = Author::with(['posts'])->get();
        $exportData = [];

        foreach ($authors as $author) {
            $record = [];
            
            foreach ($columns as $column) {
                switch ($column) {
                    case 'posts':
                        $record[$column] = $author->posts ? $author->posts->pluck('title')->implode(', ') : '';
                        break;
                    case 'post_count':
                        $record[$column] = $author->posts ? $author->posts->count() : 0;
                        break;
                    case 'full_name':
                        $record[$column] = trim($author->first_name . ' ' . $author->last_name);
                        break;
                    case 'socials':
                        $value = $author->socials;
                        $record[$column] = is_array($value) ? json_encode($value) : (string) $value;
                        break;
                    default:
                        $record[$column] = (string) $author->{$column};
                        break;
                }
            }
            
            $exportData[] = $record;
        }

        return $exportData;
    }
}
