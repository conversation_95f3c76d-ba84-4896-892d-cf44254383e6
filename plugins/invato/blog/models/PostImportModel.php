<?php

namespace Invato\Blog\Models;

use Backend\Models\ImportModel;
use Exception;

class PostImportModel extends ImportModel
{
    public $rules = [
        'title' => 'required|string|max:255',
        'content' => 'required|string',
    ];

    public function importData($results, $sessionKey = null): void
    {
        foreach ($results as $row => $data) {
            try {
                // Check if post with this title/slug already exists
                $slug = $data['slug'] ?? str_slug($data['title']);
                $post = Post::withTrashed()->where('slug', $slug)->first();

                if (! $post) {
                    $post = new Post;
                }

                $post->fill([
                    'title' => $data['title'] ?? null,
                    'slug' => $slug ?? null,
                    'content' => $data['content'] ?? null,
                    'excerpt' => $data['excerpt'] ?? null,
                    'title_short' => $data['title_short'] ?? null,
                    'status' => $data['status'] ?? 'draft',
                    'is_featured' => $data['is_featured'] ?? false,
                    'img_title' => $data['img_title'] ?? null,
                    'thumb_img_title' => $data['thumb_img_title'] ?? null,
                    'cta_title' => $data['cta_title'] ?? null,
                    'cta_content' => $data['cta_content'] ?? null,
                ]);

                // Handle JSON arrays
                if (! empty($data['additional_content'])) {
                    if (is_string($data['additional_content'])) {
                        // Parse JSON if it's a string
                        $post->additional_content = json_decode($data['additional_content'], true, 512,
                            JSON_THROW_ON_ERROR) ?: [];
                    } else {
                        $post->additional_content = $data['additional_content'];
                    }
                }

                if (! empty($data['cta_button'])) {
                    if (is_string($data['cta_button'])) {
                        // Parse JSON if it's a string
                        $post->cta_button = json_decode($data['cta_button'], true, 512,
                            JSON_THROW_ON_ERROR) ?: [];
                    } else {
                        $post->cta_button = $data['cta_button'];
                    }
                }

                $post->save();

                // Handle author if provided
                if (! empty($data['author'])) {
                    $authorName = trim($data['author']);
                    $author = Author::where('first_name', 'LIKE', "%{$authorName}%")
                        ->orWhere('last_name', 'LIKE', "%{$authorName}%")
                        ->first();

                    if ($author) {
                        $post->author_id = $author->id;
                        $post->save();
                    }
                }

                // Handle categories if provided
                if (! empty($data['categories'])) {
                    $categoryIds = [];
                    $categories = is_array($data['categories']) ? $data['categories'] : explode(',', $data['categories']);

                    foreach ($categories as $categoryName) {
                        $categoryName = trim($categoryName);
                        $category = Category::where('title', $categoryName)->first();

                        if ($category) {
                            $categoryIds[] = $category->id;
                        }
                    }

                    if (! empty($categoryIds)) {
                        $post->categories()->sync($categoryIds);
                    }
                }

                $this->logCreated();
            } catch (Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }
}
