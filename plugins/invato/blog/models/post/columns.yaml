columns:
    title:
        label: 'invato.blog::lang.post.title'
        type: text
        searchable: true
        sortable: true
    slug:
        label: 'invato.blog::lang.post.slug'
        type: text
        searchable: true
        sortable: true
        invisible: true
    status:
        label: 'invato.blog::lang.post.status'
        type: partial
        path: column_status
        searchable: true
        sortable: true
        valueFrom: status
    title_short:
        label: 'invato.blog::lang.post.titleshort'
        type: text
        searchable: true
        sortable: true
    author_id:
        label: 'invato.blog::lang.post.author'
        type: text
        relation: author
        select: 'concat(first_name, '' '', last_name)'
        searchable: true
        sortable: true
    created_at:
        label: 'invato.blog::lang.global.created'
        type: datetime
        searchable: true
        sortable: true
    updated_at:
        label: 'invato.blog::lang.global.updated'
        type: datetime
        searchable: true
        sortable: true
    # Deleted at only visible when filtering, with trashed is enabled
    deleted_at:
        label: 'Deleted at'
        invisible: true
    actions:
        label: 'invato.blog::lang.global.actions'
        type: partial
        path: column_actions
        clickable: false
        sortable: false
        align: center
        cssClass: 'column-button'
        width: '110px'
