<?php

namespace Invato\Blog\Models;

use Invato\Redirects\traits\CanRedirectModelTrait;
use Invato\Seo\traits\HasSeoableTrait;
use Model;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Validation;
use RainLab\Translate\Behaviors\TranslatableModel;

/**
 * Author Model
 */
class Author extends Model
{
    // BEGIN Skeleton model
    use CanRedirectModelTrait;
    use HasSeoableTrait;
    use SoftDelete;
    use Validation;

    protected static function booted(): void
    {
        static::deleting(static function ($author) {
            // Authors don't have detail pages, so no redirect needed
        });

        static::restored(static function ($author) {
            // Authors don't have detail pages, so no redirect needed
        });
    }

    public $table = 'invato_blog_author';

    // https://laravel.com/docs/10.x/eloquent-mutators#attribute-casting
    protected $casts = [
        'id' => 'integer',
        'first_name' => 'string',
        'last_name' => 'string',
        'bio' => 'string',
        'phone_number' => 'string',
        'email' => 'string',
        'avatar' => 'string',
        'url' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'first_name',
        'last_name',
        'bio',
        'phone_number',
        'email',
        'avatar',
        'url',
        'socials',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    // https://laravel.com/docs/10.x/validation#available-validation-rules
    public $rules = [
        'first_name' => ['required', 'string', 'max:255'],
        'last_name' => ['required', 'string', 'max:255'],
        'bio' => ['nullable', 'string'],
        'phone_number' => ['nullable', 'string', 'max:255'],
        'email' => ['nullable', 'email', 'max:255'],
        'avatar' => ['nullable', 'string', 'max:255'],
        'url' => ['nullable', 'url', 'max:255'],
    ];

    // translatable
    public $implement = [
        TranslatableModel::class,
    ];

    public $translatable = [
        'bio',
    ];

    // These attributes should not be in $casts and $rules
    protected $jsonable = [
        'socials',
    ];

    public $hasMany = [
        'posts' => Post::class,
    ];

    public $mediaAttributes = [
        'avatar',
    ];

    // END Skeleton model

    // BEGIN Model specific
    // END Model specific
}
