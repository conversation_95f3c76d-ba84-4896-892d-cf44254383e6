<?php

namespace Invato\Blog\Models;

use Backend\Models\ImportModel;
use Exception;

class CategoryImportModel extends ImportModel
{
    public $rules = [
        'title' => 'required|string|max:255',
    ];

    public function importData($results, $sessionKey = null): void
    {
        foreach ($results as $row => $data) {
            try {
                // Check if category with this title/slug already exists
                $slug = $data['slug'] ?? str_slug($data['title']);
                $category = Category::withTrashed()->where('slug', $slug)->first();

                if (! $category) {
                    $category = new Category;
                }

                $category->fill([
                    'title' => $data['title'] ?? null,
                    'slug' => $slug ?? null,
                    'description' => $data['description'] ?? null,
                    'img_title' => $data['img_title'] ?? null,
                    'sort_order' => $data['sort_order'] ?? 0,
                ]);

                $category->save();

                // Handle parent category if provided
                if (! empty($data['parent'])) {
                    $parentName = trim($data['parent']);
                    $parent = Category::where('title', $parentName)->first();

                    if ($parent) {
                        $category->parent_id = $parent->id;
                        $category->save();
                    }
                }

                // Handle posts if provided
                if (! empty($data['posts'])) {
                    $postIds = [];
                    $posts = is_array($data['posts']) ? $data['posts'] : explode(',', $data['posts']);

                    foreach ($posts as $postTitle) {
                        $postTitle = trim($postTitle);
                        $post = Post::where('title', $postTitle)->first();

                        if ($post) {
                            $postIds[] = $post->id;
                        }
                    }

                    if (! empty($postIds)) {
                        $category->posts()->sync($postIds);
                    }
                }

                $this->logCreated();
            } catch (Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }
}
