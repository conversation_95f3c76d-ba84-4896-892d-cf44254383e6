<?php return [
    'plugin' => [
        'name' => 'Blog',
        'description' => 'Create and manage blog posts',
    ],
    'author' => [
        'firstname' => 'First name',
        'lastname' => 'Last name',
        'full_name' => 'Full name',
        'avatar' => 'Avatar',
        'about' => 'About',
        'bio' => 'Bio',
        'socials' => 'Socials',
        'socials[url]' => 'URL',
        'socials[platform]' => 'Platform',
        'email' => 'Email address',
        'phonenumber' => 'Phone number',
        'posts' => 'Posts',
        'post_count' => 'Post count',
        'duplicate_confirm' => 'Are you sure you want to duplicate this author?',
        'duplicate_success' => 'Author duplicated successfully',
        'restore_confirm' => 'Are you sure you want to restore this author?',
    ],
    'global' => [
        'title' => 'Title',
        'content' => 'Content',
        'slug' => 'Slug',
        'created' => 'Created at',
        'updated' => 'Updated at',
        'categories' => 'Categories',
        'posts' => 'Posts',
        'authors' => 'Authors',
        'author' => 'Author',
        'buttons' => 'Buttons',
        'buttons_description' => 'Add a button',
        'actions' => 'Actions',
        'duplicate' => 'Duplicate',
        'delete' => 'Delete',
        'restore' => 'Restore',
    ],
    'post' => [
        'title' => 'Title',
        'slug' => 'Slug',
        'thumbnail' => 'Overview image',
        'titleshort' => 'Short title',
        'title_short' => 'Short title',
        'excerpt' => 'Excerpt',
        'content' => 'Content',
        'info' => 'Settings',
        'status' => 'Publication status',
        'status_published' => 'Published',
        'status_draft' => 'Draft',
        'categories' => 'Categories',
        'publicationdate' => 'Publication date',
        'author' => 'Author',
        'noauthor' => '-- No Author --',
        'image' => 'Image',
        'thumbnail_image' => 'Thumbnail image',
        'additional_content' => 'Additional content',
        'featured' => 'Featured',
        'is_featured' => 'Only 1 featured item possible',
        'img_title' => 'Image title',
        'thumb_img_title' => 'Thumbnail image title',
        'cta_title' => 'CTA title',
        'cta_content' => 'CTA content',
        'cta_button' => 'CTA button',
        'duplicate_confirm' => 'Are you sure you want to duplicate this post?',
        'duplicate_success' => 'Post duplicated successfully',
        'restore_confirm' => 'Are you sure you want to restore this post?',
    ],
    'category' => [
        'description' => 'Description',
        'title' => 'Title',
        'slug' => 'Slug',
        'create' => 'Create category',
        'update' => 'Update category',
        'parent' => 'Parent',
        'noparent' => '-- No parent --',
        'image' => 'Image',
        'thumbnail_image' => 'Thumbnail image',
        'img_title' => 'Image title',
        'sort_order' => 'Sort order',
        'posts' => 'Posts',
        'post_count' => 'Post count',
        'duplicate_confirm' => 'Are you sure you want to duplicate this category?',
        'duplicate_success' => 'Category duplicated successfully',
        'restore_confirm' => 'Are you sure you want to restore this category?',
    ],
    'permission' => [
        'category' => 'Manage blog categories',
        'authors' => 'Manage blog authors',
        'categories' => 'Manage categories',
        'posts' => 'Manage posts',
        'blog' => 'Manage blog',
        'import_posts' => 'Import posts',
        'export_posts' => 'Export posts',
        'import_categories' => 'Import categories',
        'export_categories' => 'Export categories',
        'import_authors' => 'Import authors',
        'export_authors' => 'Export authors',
    ],
    'import_export' => [
        'import_records' => 'Import posts',
        'export_records' => 'Export posts',
        'import_categories' => 'Import categories',
        'export_categories' => 'Export categories',
        'import_authors' => 'Import authors',
        'export_authors' => 'Export authors',
    ],
    'controller' => [
        'create' => [
            'category' => 'Create blog category',
        ],
    ],
    'postlist' => [
        'name' => 'Blog post list',
        'description' => 'Display a list of blog posts',
    ],
    'postdetail' => [
        'name' => 'Blog post',
        'description' => 'Display a single blog post',
    ],
    'categorylist' => [
        'name' => 'Blog category list',
        'description' => 'Display a list of blog categories',
    ],
    'categorydetail' => [
        'name' => 'Blog category',
        'description' => 'Display a single blog category',
    ],
    'settings' => [
        'blogsettings' => 'Blog settings',
        'blogsettingsdescription' => 'Manage global blog settings',
        'blogpages' => 'Blog pages',
        'blogpage' => 'Main blog page',
        'blogpagecomment' => 'Select the main page for the blog plugin',
        'postpage' => 'Post page',
        'postpagecomment' => 'Select the CMS page for posts',
        'categorypage' => 'Category page',
        'categorypagecomment' => 'Select the CMS page for categories',
        'design' => 'Blog design',
        'showauthor' => 'Show author',
        'showdates' => 'Show dates',
        'hidecurrentpost' => 'Hide post',
    ],
    'content' => [
        'type' => 'Content type',
        'text' => 'Text',
        'text_description' => 'Simple text field',
        'img' => 'Image',
        'img_description' => 'Image with alt tag',
        'cta' => 'Call to action',
        'img_title' => 'Image title (alt tag)',
        'thumb_img_title' => 'Thumbnail image title (alt tag)',
    ],
    'cta' => [
        'cta' => 'Call-to-action',
        'title' => 'Title',
        'content' => 'Content',
    ],
    'buttons' => [
        'text' => 'Text',
        'url' => 'Slug',
        'icon' => 'Icon',
        'icon_placeholder' => 'Choose an icon',
        'icon_position' => 'Icon position',
        'icon_position_placeholder' => 'Choose an icon position',
        'icon_position_before' => 'Before text',
        'icon_position_after' => 'After text',
        'style' => 'Style',
        'style_rounded' => 'Rounded corners',
        'style_pill' => 'Full rounded corners',
        'style_sharp' => 'Straight corners',
        'type' => 'Type',
        'type_filled' => 'Solid color',
        'type_tonal' => 'Faded color',
        'type_elevated' => 'Floating button',
        'type_outlined' => 'Outlined button',
        'type_link' => 'Link',
        'color' => 'Color',
        'color_primary' => 'Primary color',
        'color_secondary' => 'Secondary color',
        'color_white' => 'White',
        'color_grayscale' => 'Grayscale',
        'color_error' => 'Error (red)',
        'color_warning' => 'Warning (orange)',
        'color_info' => 'Info (blue)',
        'size' => 'Size',
        'size_sm' => 'Small',
        'size_base' => 'Normal',
        'size_lg' => 'Large',
        'external_link' => 'External link',
        'external_link_comment' => 'Open link in new tab',
        'extra_css' => 'Extra CSS class',
    ],
    'menuitem' => [
        'blog_post' => 'Blog post',
        'all_blog_posts' => 'All blog posts',
    ],
];
