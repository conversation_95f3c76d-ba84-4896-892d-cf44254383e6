list: $/invato/blog/models/author/columns.yaml
modelClass: Invato\Blog\Models\Author
title: Authors
noRecordsMessage: 'backend::lang.list.no_records'
showSetup: true
showCheckboxes: true
recordsPerPage: 20
toolbar:
    buttons: list_toolbar
    search:
        prompt: 'backend::lang.list.search_prompt'
recordUrl: 'invato/blog/authors/update/:id'
# BEGIN Skeleton Soft Deletes
filter: $/invato/blog/models/author/scopes.yaml
# END Skeleton Soft Deletes
