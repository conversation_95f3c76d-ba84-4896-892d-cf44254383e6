title: Categories
modelClass: Invato\Blog\Models\Category
list: $/invato/blog/models/category/columns.yaml
recordUrl: 'invato/blog/categories/update/:id'
noRecordsMessage: 'backend::lang.list.no_records'
recordsPerPage: 20
showSetup: true
showCheckboxes: true
structure:
    maxDepth: 2
toolbar:
    buttons: list_toolbar
    search:
        prompt: 'backend::lang.list.search_prompt'
# BEGIN Skeleton Soft Deletes
filter: $/invato/blog/models/category/scopes.yaml
# END Skeleton Soft Deletes
