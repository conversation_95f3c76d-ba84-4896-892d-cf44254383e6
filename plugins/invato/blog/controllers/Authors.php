<?php

namespace Invato\Blog\Controllers;

use Artisan;
use Backend\Behaviors\FormController;
use Backend\Behaviors\ImportExportController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;
use Invato\Blog\Models\Author;
use Invato\PluginDuplicateTrait;
use Invato\PluginImportExportTrait;
use Invato\PluginSoftDeleteTrait;
use October\Rain\Support\Facades\Flash;
use RuntimeException;

class Authors extends Controller
{
    use PluginDuplicateTrait;
    use PluginImportExportTrait;
    use PluginSoftDeleteTrait;

    public $implement = [
        FormController::class,
        ListController::class,
        ImportExportController::class,
    ];

    public static string $modelClass = Author::class;

    public $formConfig = 'config_form.yaml';

    public $listConfig = 'config_list.yaml';

    public string $importExportConfig = 'config_import_export.yaml';

    public string $importPermission = 'invato.blog.import_authors';

    public string $exportPermission = 'invato.blog.export_authors';

    public $requiredPermissions = [
        'invato.blog.manage_authors',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Blog', 'blog', 'authors');
    }

    public function onDuplicate()
    {
        if (! $id = post('id')) {
            throw new RuntimeException('ID not specified');
        }

        $event = Author::find($id);
        if (! $event) {
            throw new RuntimeException('Event not found');
        }

        $newModel = $event->replicate();
        $newModel->save();

        Flash::success(trans('invato.blog::lang.author.duplicate_success'));

        return $this->listRefresh();
    }

    public function onSync()
    {
        // Voer de synchronisatie uit
        $result = Artisan::call('invato:blog:sync-from-api-endpoint');

        // Toon een flash bericht
        \Flash::success('Blog auteurs zijn gesynchroniseerd.');

        // Herlaad de lijst
        return $this->listRefresh();
    }
}
