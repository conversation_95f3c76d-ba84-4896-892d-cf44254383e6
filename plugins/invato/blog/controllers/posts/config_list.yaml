list: $/invato/blog/models/post/columns.yaml
modelClass: Invato\Blog\Models\Post
title: Posts
noRecordsMessage: 'backend::lang.list.no_records'
showSetup: true
showCheckboxes: true
recordsPerPage: 20
toolbar:
    buttons: list_toolbar
    search:
        prompt: 'backend::lang.list.search_prompt'
recordUrl: 'invato/blog/posts/update/:id'
# BEGIN Skeleton Soft Deletes
filter: $/invato/blog/models/post/scopes.yaml
# END Skeleton Soft Deletes
