<?php

namespace Invato\Blog\Controllers;

use Artisan;
use Backend\Behaviors\FormController;
use Backend\Behaviors\ImportExportController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;
use Flash;
use Invato\Blog\Models\Post;
use Invato\PluginDuplicateTrait;
use Invato\PluginImportExportTrait;
use Invato\PluginSoftDeleteTrait;

class Posts extends Controller
{
    use PluginDuplicateTrait;
    use PluginImportExportTrait;
    use PluginSoftDeleteTrait;

    public $implement = [
        FormController::class,
        ListController::class,
        ImportExportController::class,
    ];

    public static string $modelClass = Post::class;

    public $formConfig = 'config_form.yaml';

    public $listConfig = 'config_list.yaml';

    public string $importExportConfig = 'config_import_export.yaml';

    public string $importPermission = 'invato.blog.import_posts';

    public string $exportPermission = 'invato.blog.export_posts';

    public $requiredPermissions = [
        'invato.blog.manage_posts',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Blog', 'blog', 'posts');
    }

    public function onSync()
    {
        // Voer de synchronisatie uit
        $result = Artisan::call('invato:blog:sync-from-api-endpoint');

        // Toon een flash bericht
        Flash::success('Blog posts zijn gesynchroniseerd.');

        // Herlaad de lijst
        return $this->listRefresh();
    }
}
