columns:
    name:
        label: 'invato.testimonials::lang.review.name'
        type: text
        searchable: true
        sortable: true
    date:
        label: 'invato.testimonials::lang.review.date'
        type: date
        format: d-m-Y
        sortable: true
    score:
        label: 'invato.testimonials::lang.review.score'
        type: number
        sortable: true
    is_approved:
        label: 'invato.testimonials::lang.review.is_approved'
        type: switch
        sortable: true
    created_at:
        label: 'Created at'
        type: date
        sortable: true
        format: d-m-Y
    updated_at:
        label: 'Updated at'
        type: date
        sortable: true
        format: d-m-Y
    # Deleted at only visible when filtering, with trashed is enabled
    deleted_at:
        label: 'Deleted at'
        invisible: true
    actions:
        label: 'Actions'
        type: partial
        path: column_actions
        clickable: false
        sortable: false
        align: center
        cssClass: 'column-button'
        width: '110px'
