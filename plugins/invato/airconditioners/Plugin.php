<?php namespace Invato\Airconditioners;

use Invato\Airconditioners\Components\AircoDetail;
use Invato\Airconditioners\Components\AircoList;
use Invato\Airconditioners\Models\AircoSettings;
use Invato\Traits\RegistersPageFinderTrait;
use Invato\Traits\SettingsMenuContextTrait;
use System\Classes\PluginBase;

class Plugin extends PluginBase
{
    use RegistersPageFinderTrait;
    use SettingsMenuContextTrait;

    public function boot(): void
    {
        $this->setupSettingsMenuContext('settings');
        $this->registerPageFinder();
    }

    public function register(): void {}

    public function registerComponents(): array
    {
        return [
            AircoDetail::class => 'AircoDetail',
            AircoList::class => 'AircoList',
        ];
    }

    public function registerSettings(): array
    {
        return [
            'settings' => [
                'label' => trans('invato.airconditioners::lang.settings.label'),
                'description' => trans('invato.airconditioners::lang.settings.description'),
                'category' => 'Plugins',
                'icon' => 'ph ph-wind',
                'class' => AircoSettings::class,
            ],
        ];
    }

    /**
     * Get PageFinder configuration for this plugin
     */
    protected function getPageFinderConfig(): array
    {
        return [
            'model' => \Invato\Airconditioners\Models\Airco::class,
            'menu_types' => [
                'airconditioners-airco' => 'invato.airconditioners::lang.menu_types.airco',
                'all-airconditioners-aircos' => 'invato.airconditioners::lang.menu_types.all_aircos',
            ]
        ];
    }
}
