<?php

namespace Invato\Airconditioners\Models;

use Backend\Models\ExportModel;

class AircoExportModel extends ExportModel
{
    public function exportData($columns, $sessionKey = null): array
    {
        $aircos = Airco::all();
        $exportData = [];

        foreach ($aircos as $airco) {
            $record = [];

            foreach ($columns as $column) {
                // Handle JSON fields
                if (in_array($column, ['images', 'specifications', 'custom_options'])) {
                    $value = $airco->{$column};
                    $record[$column] = is_array($value) ? json_encode($value) : ($value ?: '[]');
                } else {
                    $record[$column] = $airco->{$column};
                }
            }

            $exportData[] = $record;
        }

        return $exportData;
    }
}
