<?php

namespace Invato\Airconditioners\Models;

use Backend\Models\ImportModel;
use Exception;

class AircoImportModel extends ImportModel
{
    public $rules = [
        'title' => 'required|string|max:255',
    ];

    public function importData($results, $sessionKey = null): void
    {
        foreach ($results as $row => $data) {
            try {
                // Check if airco with this title/slug already exists
                $slug = $data['slug'] ?? str_slug($data['title']);
                $airco = Airco::withTrashed()->where('slug', $slug)->first();

                if (! $airco) {
                    $airco = new Airco;
                }

                $airco->fill([
                    'title' => $data['title'] ?? null,
                    'slug' => $slug ?? null,
                    'description' => $data['description'] ?? null,
                    'capacity' => $data['capacity'] ?? 0.0,
                    'price' => $data['price'] ?? 0.00,
                    'price_new' => $data['price_new'] ?? null,
                    'energylabel_cooling' => $data['energylabel_cooling'] ?? null,
                    'energylabel_heating' => $data['energylabel_heating'] ?? null,
                    'is_active' => $data['is_active'] ?? true,
                    'installation_included' => $data['installation_included'] ?? false,
                    'brochure' => $data['brochure'] ?? null,
                    'manual' => $data['manual'] ?? null,
                    'technical_manual' => $data['technical_manual'] ?? null,
                    'sort_order' => $data['sort_order'] ?? 0,
                ]);

                // Handle JSON fields
                if (isset($data['images'])) {
                    if (is_string($data['images'])) {
                        $airco->images = json_decode($data['images'], true) ?: [];
                    } else {
                        $airco->images = $data['images'] ?: [];
                    }
                }

                if (isset($data['specifications'])) {
                    if (is_string($data['specifications'])) {
                        $airco->specifications = json_decode($data['specifications'], true) ?: [];
                    } else {
                        $airco->specifications = $data['specifications'] ?: [];
                    }
                }

                if (isset($data['custom_options'])) {
                    if (is_string($data['custom_options'])) {
                        $airco->custom_options = json_decode($data['custom_options'], true) ?: [];
                    } else {
                        $airco->custom_options = $data['custom_options'] ?: [];
                    }
                }

                $airco->save();

                $this->logCreated();
            } catch (Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }
}
