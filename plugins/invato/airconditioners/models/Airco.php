<?php namespace Invato\Airconditioners\Models;

use Invato\Airconditioners\Components\AircoDetail;
use Invato\Redirects\traits\CanRedirectModelTrait;
use Invato\Seo\traits\HasSeoableTrait;
use Invato\Traits\HasPageFinderTrait;
use Model;
use October\Rain\Database\Traits\Sluggable;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;

/**
 * Model
 */
class Airco extends Model
{
    use CanRedirectModelTrait;
    use HasPageFinderTrait;
    use HasSeoableTrait;
    use Sluggable;
    use SoftDelete;
    use Sortable;
    use Validation;

    protected static function booted(): void
    {
        static::deleting(static function ($airco) {
            static::createRedirect(
                plugin: 'airconditioners',
                modelRecord: $airco,
                detailPageController: AircoDetail::class,
                status: 301
            );
        });

        static::restored(static function ($airco) {
            static::deleteRedirect($airco);
        });
    }
    // https://laravel.com/docs/10.x/eloquent-mutators#attribute-casting
    public $table = 'invato_airconditioners_aircos';

    protected $casts = [
        'id' => 'integer',
        'title' => 'string',
        'slug' => 'string',
        'description' => 'string',
        'capacity' => 'decimal:1',
        'price' => 'decimal:2',
        'price_new' => 'decimal:2',
        'energylabel_cooling' => 'string',
        'energylabel_heating' => 'string',
        'is_active' => 'boolean',
        'installation_included' => 'boolean',
        'brochure' => 'string',
        'manual' => 'string',
        'technical_manual' => 'string',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'title',
        'slug',
        'description',
        'capacity',
        'price',
        'price_new',
        'images',
        'energylabel_cooling',
        'energylabel_heating',
        'specifications',
        'is_active',
        'installation_included',
        'brochure',
        'manual',
        'technical_manual',
        'sort_order',
        'custom_options',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    // https://laravel.com/docs/10.x/validation#available-validation-rules
    public $rules = [
        'title' => ['required', 'string', 'max:255'],
        'slug' => ['required', 'string', 'max:255', 'unique:invato_airconditioners_aircos,slug,{{id}}'],
        'description' => ['nullable', 'string'],
        'price' => ['required', 'numeric', 'decimal:0,2'],
        'price_new' => ['nullable', 'numeric', 'decimal:0,2'],
        'capacity' => ['required', 'numeric', 'decimal:0,1'],
        'energylabel_cooling' => ['nullable', 'string', 'max:255'],
        'energylabel_heating' => ['nullable', 'string', 'max:255'],
        'is_active' => ['boolean'],
        'installation_included' => ['boolean'],
        'brochure' => ['nullable', 'string'],
        'manual' => ['nullable', 'string'],
        'technical_manual' => ['nullable', 'string'],
        'sort_order' => ['integer'],
    ];

    // translatable
    public $implement = [
        \RainLab\Translate\Behaviors\TranslatableModel::class
    ];
    public $translatable = [
        'title',
        'slug',
        'description',
    ];

    protected array $slugs = [
        'slug' => 'title',
    ];

    // These attributes should not be in $casts and $rules
    protected $jsonable = [
        'images',
        'specifications',
        'custom_options',
    ];

    /**
     * Get PageFinder configuration for Airco model
     */
    protected static function getPageFinderConfig(): array
    {
        return [
            'single_type' => 'airconditioners-airco',
            'all_type' => 'all-airconditioners-aircos',
            'component' => 'AircoDetail',
        ];
    }
}
