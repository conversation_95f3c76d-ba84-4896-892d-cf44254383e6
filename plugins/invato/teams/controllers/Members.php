<?php

namespace Invato\Teams\Controllers;

use Backend\Behaviors\FormController;
use Backend\Behaviors\ImportExportController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;
use Invato\PluginDuplicateTrait;
use Invato\PluginImportExportTrait;
use Invato\PluginSoftDeleteTrait;
use Invato\Teams\Models\Member;

class Members extends Controller
{
    use PluginDuplicateTrait;
    use PluginImportExportTrait;
    use PluginSoftDeleteTrait;

    public $implement = [
        FormController::class,
        ListController::class,
        ImportExportController::class,
    ];

    public static string $modelClass = Member::class;

    public string $formConfig = 'config_form.yaml';

    public string $listConfig = 'config_list.yaml';

    public string $importExportConfig = 'config_import_export.yaml';

    public string $importPermission = 'invato.teams.import_members';

    public string $exportPermission = 'invato.teams.export_members';

    public $requiredPermissions = [
        'invato.teams.manage_members',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Teams', 'teams', 'members');
    }
}
