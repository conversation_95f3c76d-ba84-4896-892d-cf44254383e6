<div class="d-flex gap-1 justify-content-center">
    <button data-tooltip-text="<?= e(__('Duplicate')) ?>"
            data-request="onDuplicate"
            data-request-data="id: '<?= $record->id ?>'"
            data-request-confirm="<?= e(__('Duplicate confirmation')) ?>"
            data-stripe-load-indicator
            class="btn btn-primary btn-sm"><i class="octo-icon-copy"></i></button>

    <?php if ($record->deleted_at) { ?>
        <!-- Restore button -->
        <button data-tooltip-text="<?= e(__('Restore')) ?>"
                data-request="onRestore"
                data-request-data="id: '<?= $record->id ?>'"
                data-request-confirm="<?= e(__('Restore confirmation')) ?>"
                data-stripe-load-indicator
                class="btn btn-success btn-sm ms-1"><i class="octo-icon-refresh"></i></button>
    <?php } else { ?>
        <!-- Delete button -->
        <button data-tooltip-text="<?= e(__('Delete')) ?>"
                data-request="onDelete"
                data-request-data="checked: ['<?= $record->id ?>']"
                data-request-confirm="<?= e(__('Delete confirmation')) ?>"
                data-stripe-load-indicator
                class="btn btn-danger btn-sm ms-1"><i class="octo-icon-delete"></i></button>
    <?php } ?>
</div>
