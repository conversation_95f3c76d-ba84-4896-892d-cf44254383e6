list: $/invato/teams/models/department/columns.yaml
modelClass: Invato\Teams\Models\Department
title: Departments
noRecordsMessage: 'backend::lang.list.no_records'
showSetup: true
showCheckboxes: true
recordsPerPage: 20
structure:
    showTree: false
    showReorder: false
    showSorting: false
    maxDepth: 1
toolbar:
    buttons: list_toolbar
    search:
        prompt: 'backend::lang.list.search_prompt'
recordUrl: 'invato/teams/departments/update/:id'
# BEGIN Skeleton Soft Deletes
filter: $/invato/teams/models/department/scopes.yaml
# END Skeleton Soft Deletes
