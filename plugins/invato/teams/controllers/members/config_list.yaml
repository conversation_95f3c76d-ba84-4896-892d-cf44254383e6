list: $/invato/teams/models/member/columns.yaml
modelClass: Invato\Teams\Models\Member
title: Members
noRecordsMessage: 'backend::lang.list.no_records'
showSetup: true
showCheckboxes: true
recordsPerPage: 20
structure:
    showTree: true
    showReorder: true
    showSorting: true
    maxDepth: 1
toolbar:
    buttons: list_toolbar
    search:
        prompt: 'backend::lang.list.search_prompt'
recordUrl: 'invato/teams/members/update/:id'
# BEGIN Skeleton Soft Deletes
filter: $/invato/teams/models/member/scopes.yaml
# END Skeleton Soft Deletes
