<?php

namespace Invato\Teams\Models;

use Backend\Models\ExportModel;

class DepartmentExportModel extends ExportModel
{
    public function exportData($columns, $sessionKey = null)
    {
        $departments = Department::all();
        $departments->each(function ($department) use ($columns) {
            $department->addVisible($columns);
        });

        return $departments->toArray();
    }
}
