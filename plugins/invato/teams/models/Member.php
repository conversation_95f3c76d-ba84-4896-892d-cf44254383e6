<?php

namespace Invato\Teams\Models;

use Invato\Redirects\traits\CanRedirectModelTrait;
use Invato\Teams\Components\MemberDetail;
use Invato\Traits\HasPageFinderTrait;
use Model;
use October\Rain\Database\Traits\SimpleTree;
use October\Rain\Database\Traits\Sluggable;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;
use October\Rain\Exception\ValidationException;
use RainLab\Translate\Behaviors\TranslatableModel;

class Member extends Model
{
    use CanRedirectModelTrait;
    use HasPageFinderTrait;
    use SimpleTree;
    use Sluggable;
    use SoftDelete;
    use Sortable;
    use Validation;

    protected static function booted(): void
    {
        static::deleting(static function (self $member) {
            static::createRedirect(
                plugin: 'teams',
                modelRecord: $member,
                detailPageController: MemberDetail::class,
            );
        });

        static::restored(static function (self $member) {
            static::deleteRedirect($member);
        });
    }

    public $implement = [
        TranslatableModel::class,
    ];

    public $translatable = ['title', 'description'];

    public $table = 'invato_teams_members';

    protected $casts = [
        'id' => 'integer',
        'name' => 'string',
        'surname' => 'string',
        'slug' => 'string',
        'img' => 'string',
        'title' => 'string',
        'description' => 'string',
        'phonenumber' => 'string',
        'email' => 'string',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'parent_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'name',
        'surname',
        'slug',
        'img',
        'title',
        'description',
        'phonenumber',
        'email',
        'is_active',
        'sort_order',
        'parent_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public $rules = [
        'name' => ['required', 'string', 'max:255'],
        'surname' => ['nullable', 'string', 'max:255'],
        'slug' => ['required', 'string', 'max:255', 'unique:invato_teams_members,slug,{{id}}'],
        'img' => ['nullable', 'string'],
        'title' => ['nullable', 'string', 'max:255'],
        'description' => ['nullable', 'string'],
        'phonenumber' => ['nullable', 'string', 'max:255'],
        'email' => ['nullable', 'email', 'max:255'],
        'is_active' => ['boolean'],
        'sort_order' => ['integer'],
        'parent_id' => ['nullable', 'integer'],
    ];

    protected array $slugs = [
        'slug' => 'name',
    ];

    protected $jsonable = [
        'socials',
    ];

    public $belongsToMany = [
        'departments' => [Department::class, 'table' => 'invato_teams_department_member'],
    ];

    /**
     * Get PageFinder configuration for Member model
     */
    protected static function getPageFinderConfig(): array
    {
        return [
            'single_type' => 'teams-member',
            'all_type' => 'all-teams-members',
            'component' => 'MemberDetail',
        ];
    }

    public function beforeCreate(): void
    {
        self::checkForLimit();
    }

    public function beforeRestore(): void
    {
        self::checkForLimit(true);
    }

    public static function checkForLimit(bool $isRestoring = false): void
    {
        $existingMembersCount = static::withoutTrashed()->count();
        $limit = TeamsAdminSettings::get('members_limit');
        if (
            isset($limit)
            &&
            ($existingMembersCount + ($isRestoring ? 1 : 0)) >= $limit
        ) {
            throw new ValidationException(["Er mogen maximaal $limit teamleden aanwezig zijn. Verwijder een teamlid om een nieuwe aan te kunnen maken. Of neem contact op met Invato, via <EMAIL>, om een hoger limit te krijgen."]);
        }
    }

    public function getFullNameAttribute(): string
    {
        return trim($this->name.' '.$this->surname);
    }
}
