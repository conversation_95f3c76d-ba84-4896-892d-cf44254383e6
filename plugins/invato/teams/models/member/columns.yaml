columns:
    name:
        label: 'invato.teams::lang.columns.name'
        type: text
        sortable: true
    surname:
        label: 'invato.teams::lang.columns.surname'
        type: text
        searchable: true
        sortable: true
    title:
        label: 'invato.teams::lang.columns.title'
        type: text
        searchable: true
        sortable: true
    is_active:
        label: 'invato.teams::lang.columns.is_active'
        type: text
        searchable: true
        sortable: true
    created_at:
        label: 'invato.teams::lang.columns.created_at'
        type: datetime
        sortable: true
    updated_at:
        label: 'invato.teams::lang.columns.updated_at'
        type: datetime
        sortable: true
    # Deleted at only visible when filtering, with trashed is enabled
    deleted_at:
        label: 'invato.teams::lang.columns.deleted_at'
        type: datetime
        invisible: true
    actions:
        label: 'Actions'
        type: partial
        path: column_actions
        clickable: false
        sortable: false
        align: center
        cssClass: 'column-button'
        width: '110px'
