<?php

namespace Invato\Teams\Models;

use Invato\Redirects\traits\CanRedirectModelTrait;
use Invato\Teams\Components\DepartmentDetail;
use Invato\Traits\HasPageFinderTrait;
use Model;
use October\Rain\Database\Traits\Sluggable;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;
use October\Rain\Exception\ValidationException;
use RainLab\Translate\Behaviors\TranslatableModel;

class Department extends Model
{
    use CanRedirectModelTrait;
    use HasPageFinderTrait;
    use Sluggable;
    use SoftDelete;
    use Sortable;
    use Validation;

    protected static function booted(): void
    {
        static::deleting(static function (self $department) {
            static::createRedirect(
                plugin: 'teams',
                modelRecord: $department,
                detailPageController: DepartmentDetail::class,
            );
        });

        static::restored(static function (self $department) {
            static::deleteRedirect($department);
        });
    }

    public $implement = [
        TranslatableModel::class,
    ];

    public $translatable = ['title', 'description'];

    public $table = 'invato_teams_departments';

    protected $casts = [
        'id' => 'integer',
        'title' => 'string',
        'slug' => 'string',
        'description' => 'string',
        'img' => 'string',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'parent_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'title',
        'slug',
        'description',
        'img',
        'is_active',
        'sort_order',
        'parent_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public $rules = [
        'title' => ['required', 'string', 'max:255'],
        'slug' => ['required', 'string', 'max:255', 'unique:invato_teams_departments,slug,{{id}}'],
        'description' => ['nullable', 'string'],
        'img' => ['nullable', 'string'],
        'is_active' => ['boolean'],
        'sort_order' => ['integer'],
        'parent_id' => ['nullable', 'integer'],
    ];

    protected array $slugs = [
        'slug' => 'title',
    ];

    public $belongsToMany = [
        'members' => [Member::class, 'table' => 'invato_teams_department_member'],
    ];

    /**
     * Get PageFinder configuration for Department model
     */
    protected static function getPageFinderConfig(): array
    {
        return [
            'single_type' => 'teams-department',
            'all_type' => 'all-teams-departments',
            'component' => 'DepartmentDetail',
        ];
    }

    public function beforeCreate(): void
    {
        self::checkForLimit();
    }

    public function beforeRestore(): void
    {
        self::checkForLimit(true);
    }

    public static function checkForLimit(bool $isRestoring = false): void
    {
        $existingMembersCount = static::withoutTrashed()->count();
        $limit = TeamsAdminSettings::get('departments_limit');
        if (
            isset($limit)
            &&
            ($existingMembersCount + ($isRestoring ? 1 : 0)) >= $limit
        ) {
            throw new ValidationException(['limit' => "Er mogen maximaal $limit afdelingen aanwezig zijn. Verwijder een afdeling om een nieuwe aan te kunnen maken. Of neem contact op met Invato, via <EMAIL>, om een hoger limit te krijgen."]);
        }
    }

    public function getParentsAttribute()
    {
        $parents = [];
        $parent = $this->parent;

        while ($parent) {
            array_unshift($parents, $parent->title);
            $parent = $parent->parent;
        }

        return implode(' > ', $parents);
    }
}
