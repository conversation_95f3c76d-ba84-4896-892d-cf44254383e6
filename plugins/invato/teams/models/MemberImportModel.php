<?php

namespace Invato\Teams\Models;

use Backend\Models\ImportModel;
use Exception;

class MemberImportModel extends ImportModel
{
    public $rules = [
        'name' => 'required|string|max:255',
    ];

    public function importData($results, $sessionKey = null): void
    {
        foreach ($results as $row => $data) {
            try {
                // Check if member with this name/slug already exists
                $slug = $data['slug'] ?? str_slug($data['name']);
                $member = Member::withTrashed()->where('slug', $slug)->first();

                if (! $member) {
                    $member = new Member;
                }

                $member->fill([
                    'name' => $data['name'] ?? null,
                    'surname' => $data['surname'] ?? null,
                    'slug' => $slug ?? null,
                    'img' => $data['img'] ?? null,
                    'title' => $data['title'] ?? null,
                    'description' => $data['description'] ?? null,
                    'phonenumber' => $data['phonenumber'] ?? null,
                    'email' => $data['email'] ?? null,
                    'is_active' => $data['is_active'] ?? true,
                    'sort_order' => $data['sort_order'] ?? 0,
                ]);

                // Handle socials array
                if (! empty($data['socials'])) {
                    if (is_string($data['socials'])) {
                        // Parse JSON if it's a string
                        $member->socials = json_decode($data['socials'], true, 512, JSON_THROW_ON_ERROR) ?: [];
                    } else {
                        $member->socials = $data['socials'];
                    }
                }

                $member->save();

                // Handle departments if provided
                if (! empty($data['departments'])) {
                    $departmentIds = [];
                    $departments = is_array($data['departments']) ? $data['departments'] : explode(',', $data['departments']);

                    foreach ($departments as $departmentName) {
                        $departmentName = trim($departmentName);
                        $department = Department::where('title', $departmentName)->first();

                        if ($department) {
                            $departmentIds[] = $department->id;
                        }
                    }

                    if (! empty($departmentIds)) {
                        $member->departments()->sync($departmentIds);
                    }
                }

                $this->logCreated();
            } catch (Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }
}
