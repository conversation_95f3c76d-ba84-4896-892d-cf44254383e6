<?php

namespace Invato\Teams\Models;

use Backend\Models\ImportModel;
use Exception;

class DepartmentImportModel extends ImportModel
{
    public $rules = [
        'title' => 'required|string|max:255',
    ];

    public function importData($results, $sessionKey = null): void
    {
        foreach ($results as $row => $data) {
            try {
                // Check if department with this title/slug already exists
                $slug = $data['slug'] ?? str_slug($data['title']);
                $department = Department::withTrashed()->where('slug', $slug)->first();

                if (! $department) {
                    $department = new Department;
                }

                $department->fill([
                    'title' => $data['title'] ?? null,
                    'slug' => $slug ?? null,
                    'description' => $data['description'] ?? null,
                    'img' => $data['img'] ?? null,
                    'is_active' => $data['is_active'] ?? true,
                    'sort_order' => $data['sort_order'] ?? 0,
                ]);

                $department->save();

                $this->logCreated();
            } catch (Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }
}
