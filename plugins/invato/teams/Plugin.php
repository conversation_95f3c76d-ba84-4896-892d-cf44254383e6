<?php

namespace Invato\Teams;

use Event;
use Invato\Teams\Models\Department;
use Invato\Teams\Models\Member;
use Invato\Teams\Models\TeamsAdminSettings;
use Invato\Teams\Models\TeamsSettings;
use Invato\Traits\RegistersPageFinderTrait;
use Invato\Traits\SettingsMenuContextTrait;
use OFFLINE\Boxes\Models\Box;
use System\Classes\PluginBase;

class Plugin extends PluginBase
{
    use RegistersPageFinderTrait;
    use SettingsMenuContextTrait;

    public function register(): void {}

    public function boot(): void
    {
        $this->setupSettingsMenuContext('settings');
        $this->setupSettingsMenuContext('admin-settings');
        $this->registerPageFinder();

        Box::extend(function ($model) {
            $model->addDynamicMethod('listAllDepartments', function ($value) {
                return Department::get()->lists('title', 'id');
            });
        });

        Event::listen('backend.form.extendFields', function ($widget) {
            if ($widget->context === 'create') {
                if ($widget->model instanceof Department) {
                    Department::checkForLimit();
                }
                if ($widget->model instanceof Member) {
                    Member::checkForLimit();
                }
            }
        });
    }

    /**
     * registerComponents used by the frontend.
     */
    public function registerComponents(): array
    {
        return [
            'Invato\Teams\Components\TeamsList' => 'TeamsList',
            'Invato\Teams\Components\DepartmentList' => 'DepartmentList',
            'Invato\Teams\Components\DepartmentSelect' => 'DepartmentSelect',
            'Invato\Teams\Components\DepartmentDetail' => 'DepartmentDetail',
            'Invato\Teams\Components\MemberList' => 'MemberList',
            'Invato\Teams\Components\MemberDetail' => 'MemberDetail',
        ];
    }

    public function registerSnippets(): array
    {
        return [
            'Invato\Teams\Components\TeamsList' => 'TeamsList',
            'Invato\Teams\Components\DepartmentList' => 'DepartmentList',
            'Invato\Teams\Components\DepartmentSelect' => 'DepartmentSelect',
            'Invato\Teams\Components\DepartmentDetail' => 'DepartmentDetail',
            'Invato\Teams\Components\MemberList' => 'MemberList',
            'Invato\Teams\Components\MemberDetail' => 'MemberDetail',
        ];
    }

    /**
     * registerSettings used by the backend.
     */
    public function registerSettings()
    {
        return [
            'settings' => [
                'label' => 'invato.teams::lang.settings.teams',
                'description' => 'invato.teams::lang.settings.description',
                'category' => 'Plugins',
                'icon' => 'icon-users',
                'size' => 'adaptive',
                'class' => TeamsSettings::class,
            ],
            'admin-settings' => [
                'label' => 'invato.teams::lang.settings.teams_admin',
                'description' => 'invato.teams::lang.settings.description',
                'category' => 'Site Configuratie',
                'icon' => 'icon-users',
                'size' => 'adaptive',
                'class' => TeamsAdminSettings::class,
                'permissions' => ['invato.teams.manage_admin_settings'],
            ],
        ];
    }

    /**
     * Get PageFinder configuration for this plugin
     */
    protected function getPageFinderConfig(): array
    {
        return [
            'model' => Department::class,
            'menu_types' => [
                'teams-department' => 'invato.teams::lang.pagefinder.department_single',
                'all-teams-departments' => 'invato.teams::lang.pagefinder.departments_all',
                'teams-member' => 'invato.teams::lang.pagefinder.member_single',
                'all-teams-members' => 'invato.teams::lang.pagefinder.members_all',
            ],
        ];
    }
}
