# Plugin: Teams

## Doel:
Een centrale plek voor het beheren van afdelingen en teamleden zodat deze op een ge-structureerde manier weergegeven kan worden op de website.

## Backend:
De plugin heeft 2 instellingen pagina’s, die in het linkermenu onder kopje Teams en 1 onder Site instellingen->Teams admin instellingen:

### Teams pagina's:
* #### Afdeling
  Hier kunnen de verschillende afdelingen binnen het bedrijf opgevoerd worden of het kan gebruikt worden als filter/categorisering optie voor de teamleden.
* #### Teamleden
  Hier kunnen de teamleden opgevoerd worden.

### Wie heeft toegang?
* Invato
* <PERSON><PERSON> met toegang tot de plugin

### Teams admin instellingen:
Hier zijn de limieten in te stellen voor de afdelingen en teamleden.

* #### Departments Limit
  Hier kan een getal ingevuld worden die het limiet zet voor het aanmaken van een afdelingen.
  * Laat deze leeg voor geen limiet
  * Bij het #0 is het niet mogelijk om een afdeling aan te maken
* #### Members Limit
  Hier kan een getal ingevuld worden die het limiet zet voor het aanmaken van een teamlid.
    * Laat deze leeg voor geen limiet
    * Bij het #0 is het niet mogelijk om een teamlid aan te maken

In het geval dat een limiet bereikt wordt geeft de plugin een melding dat er eerst een item verwijderd dient te worden of dat er contact opgenomen moet worden met Invato.

### Wie heeft toegang?
* Invato

## Front-end:
Deze plugin bevat 10 boxes die elk een andere structuur hebben.
Hieronder een lijst met functies van de boxes.

* ### Dynamisch filter:
    Alle boxes zijn voorzien van een dynamisch filter waarin in de backend een afdeling gekozen kan worden. Als deze keuze gemaakt is dan zijn alleen teamleden waarbij die afdeling aangevinkt is zichtbaar.
* ### front-end filter:
    Box 1, 2, 3, 4, 6, 7, 8 en 9 zijn voorzien van een front-end filter optie als er in de backend geen dynamisch filter gezet is. Als er een afdelingen aangemaakt zijn dan is dit filter niet zichtbaar.
* ### Paginatie:
    Box 1, 2, 3, 4, 6, 7, 8 en 9 zijn voorzien van een paginatie optie en daarbij een keuze veld voor het aantal items per pagina.
* ### Sliders:
    Box 5 en 10 zijn voorzien van een slider optie. Voor deze 2 boxen is het niet aan te raden grote teams te gebruiken. Hier zou het beste een dynamisch filter ingezet kunnen worden waarin voor een specifieke afdeling gekozen wordt.

### Wie heeft toegang?
* Invato
* Klant met toegang tot de boxes en plugin.

## Functionaliteiten:

### Import/Export
- Afdelingen kunnen worden geïmporteerd en geëxporteerd via CSV bestanden
- Teamleden kunnen worden geïmporteerd en geëxporteerd via CSV bestanden
- Ondersteuning voor bulk operaties

### Soft Deletes
- Verwijderde afdelingen en teamleden worden niet permanent verwijderd
- Mogelijkheid om verwijderde items te herstellen
- Filter optie om verwijderde items te tonen

### Duplicatie
- Afdelingen en teamleden kunnen worden gedupliceerd
- Automatische slug generatie bij duplicatie

### Hiërarchische structuur
- Afdelingen ondersteunen hiërarchische organisatie
- Teamleden kunnen worden georganiseerd in een boom structuur

### PageFinder integratie
- Automatische integratie met PageFinder voor CMS pagina's
- Ondersteuning voor dynamische URL's en routing

### Redirects integratie
- Automatische redirects bij verwijdering van items met detail pagina's
- Integratie met Invato Redirects plugin

## Gebruikte unieke permissies:

- `invato.teams.manage_teams` - Algemene toegang tot teams functionaliteit
- `invato.teams.manage_departments` - Beheer van afdelingen
- `invato.teams.manage_members` - Beheer van teamleden
- `invato.teams.import_departments` - Import van afdelingen
- `invato.teams.export_departments` - Export van afdelingen
- `invato.teams.import_members` - Import van teamleden
- `invato.teams.export_members` - Export van teamleden
- `invato.teams.manage_admin_settings` - Beheer van admin instellingen
