<?php

namespace Invato\Teams\Components;

use Cms\Classes\ComponentBase;
use Invato\Teams\Models\Member;

class MemberDetail extends ComponentBase
{
    public Member $member;

    public function componentDetails(): array
    {
        return [
            'name' => 'Member Detail',
            'description' => 'Displays a single team member with details',
        ];
    }

    public function defineProperties(): array
    {
        return [
            'slug' => [
                'title' => 'Member Slug',
                'description' => 'Look up the member using the supplied slug value.',
                'default' => '{{ :slug }}',
                'type' => 'string',
            ],
        ];
    }

    public function onRun(): void
    {
        $this->member = $this->loadMember();
    }

    protected function loadMember(): ?Member
    {
        $slug = $this->property('slug');

        return Member::where('slug', $slug)
            ->where('is_active', true)
            ->first();
    }

    public function getMember(): ?Member
    {
        return $this->member ?? null;
    }
}
