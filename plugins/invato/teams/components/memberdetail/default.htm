{% set member = __SELF__.getMember() %}

{% if member %}
    <div class="member-detail">
        <div class="member-header">
            {% if member.img %}
                <img src="{{ member.img|media }}" alt="{{ member.full_name }}" class="member-image">
            {% endif %}
            <div class="member-info">
                <h1>{{ member.full_name }}</h1>
                {% if member.title %}
                    <p class="member-title">{{ member.title }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="member-contact">
            {% if member.email %}
                <p class="member-email">
                    <strong>Email:</strong> <a href="mailto:{{ member.email }}">{{ member.email }}</a>
                </p>
            {% endif %}
            {% if member.phonenumber %}
                <p class="member-phone">
                    <strong>Phone:</strong> <a href="tel:{{ member.phonenumber }}">{{ member.phonenumber }}</a>
                </p>
            {% endif %}
        </div>
        
        {% if member.description %}
            <div class="member-description">
                {{ member.description|raw }}
            </div>
        {% endif %}
        
        {% if member.departments.count > 0 %}
            <div class="member-departments">
                <h3>Departments</h3>
                <ul>
                    {% for department in member.departments %}
                        {% if department.is_active %}
                            <li>{{ department.title }}</li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
        
        {% if member.socials %}
            <div class="member-socials">
                <h3>Social Media</h3>
                <ul class="social-links">
                    {% for social in member.socials %}
                        <li>
                            <a href="{{ social.slug }}" target="_blank" rel="noopener">
                                {{ social.title }}
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
    </div>
{% else %}
    <p>Member not found.</p>
{% endif %}
