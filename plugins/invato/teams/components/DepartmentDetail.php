<?php

namespace Invato\Teams\Components;

use Cms\Classes\ComponentBase;
use Invato\Teams\Models\Department;

class DepartmentDetail extends ComponentBase
{
    public Department $department;

    public function componentDetails(): array
    {
        return [
            'name' => 'Department Detail',
            'description' => 'Displays a single department with details',
        ];
    }

    public function defineProperties(): array
    {
        return [
            'slug' => [
                'title' => 'Department Slug',
                'description' => 'Look up the department using the supplied slug value.',
                'default' => '{{ :slug }}',
                'type' => 'string',
            ],
        ];
    }

    public function onRun(): void
    {
        $this->department = $this->loadDepartment();
    }

    protected function loadDepartment(): ?Department
    {
        $slug = $this->property('slug');

        return Department::where('slug', $slug)
            ->where('is_active', true)
            ->first();
    }

    public function getDepartment(): ?Department
    {
        return $this->department ?? null;
    }
}
