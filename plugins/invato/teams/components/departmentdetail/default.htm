{% set department = __SELF__.getDepartment() %}

{% if department %}
    <div class="department-detail">
        <h1>{{ department.title }}</h1>
        
        {% if department.img %}
            <img src="{{ department.img|media }}" alt="{{ department.title }}" class="department-image">
        {% endif %}
        
        {% if department.description %}
            <div class="department-description">
                {{ department.description|raw }}
            </div>
        {% endif %}
        
        {% if department.members.count > 0 %}
            <div class="department-members">
                <h2>Team Members</h2>
                <div class="members-grid">
                    {% for member in department.members %}
                        {% if member.is_active %}
                            <div class="member-card">
                                {% if member.img %}
                                    <img src="{{ member.img|media }}" alt="{{ member.name }}" class="member-image">
                                {% endif %}
                                <h3>{{ member.full_name }}</h3>
                                {% if member.title %}
                                    <p class="member-title">{{ member.title }}</p>
                                {% endif %}
                                {% if member.email %}
                                    <p class="member-email">
                                        <a href="mailto:{{ member.email }}">{{ member.email }}</a>
                                    </p>
                                {% endif %}
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>
{% else %}
    <p>Department not found.</p>
{% endif %}
