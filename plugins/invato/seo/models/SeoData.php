<?php

namespace Invato\Seo\Models;

use Model;
use October\Rain\Database\Traits\Multisite;
use October\Rain\Database\Traits\Validation;

/**
 * Model
 */
class SeoData extends Model
{
    use Multisite;
    use Validation;

    /**
     * @var string table in the database used by the model.
     */
    public $table = 'invato_seo_data';

    public $rules = [];

    protected $casts = [
        'id' => 'integer',
        'seoable_type' => 'string',
        'seoable_id' => 'integer',
        'data' => 'json',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $fillable = [
        'seoable_type',
        'seoable_id',
        'data',
    ];

    public $morphTo = [
        'seoable' => [],
    ];

    protected $propagatable = [];
}
