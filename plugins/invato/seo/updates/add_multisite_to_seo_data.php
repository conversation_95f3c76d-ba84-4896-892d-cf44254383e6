<?php

namespace Invato\Seo\Updates;

use October\Rain\Database\Updates\Migration;
use Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('invato_seo_data', function ($table) {
            $table->integer('site_id')->nullable()->after('data')->index();
            $table->integer('site_root_id')->nullable()->after('site_id')->index();
        });
    }

    public function down(): void
    {
        Schema::table('invato_seo_data', function ($table) {
            $table->dropColumn(['site_id', 'site_root_id']);
        });
    }
};
