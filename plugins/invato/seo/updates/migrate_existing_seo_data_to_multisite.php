<?php

namespace Invato\Seo\Updates;

use DB;
use October\Rain\Database\Updates\Migration;
use System\Models\SiteDefinition;

return new class extends Migration
{
    public function up()
    {
        // Get all enabled sites
        $sites = SiteDefinition::all();

        if ($sites->count() <= 1) {
            // Only one site, just update existing records
            DB::table('invato_seo_data')
                ->whereNull('site_id')
                ->update([
                    'site_id' => $sites->first()->id ?? 1,
                    'site_root_id' => DB::raw('id'),
                ]);

            return;
        }

        // Get all existing SEO data records that don't have site_id set
        $existingRecords = DB::table('invato_seo_data')
            ->whereNull('site_id')
            ->get();

        foreach ($existingRecords as $record) {
            $isFirst = true;
            $rootId = $record->id;

            foreach ($sites as $site) {
                if ($isFirst) {
                    // Update the original record for the first site
                    DB::table('invato_seo_data')
                        ->where('id', $record->id)
                        ->update([
                            'site_id' => $site->id,
                            'site_root_id' => $rootId,
                        ]);
                    $isFirst = false;
                } else {
                    // Create duplicate records for other sites
                    DB::table('invato_seo_data')->insert([
                        'seoable_type' => $record->seoable_type,
                        'seoable_id' => $record->seoable_id,
                        'data' => $record->data,
                        'site_id' => $site->id,
                        'site_root_id' => $rootId,
                        'created_at' => $record->created_at,
                        'updated_at' => now(),
                    ]);
                }
            }
        }
    }

    public function down()
    {
        // Remove duplicated records, keep only the original ones
        DB::table('invato_seo_data')
            ->where('site_id', '!=', DB::raw('site_root_id'))
            ->delete();

        // Clear site columns from remaining records
        DB::table('invato_seo_data')->update([
            'site_id' => null,
            'site_root_id' => null,
        ]);
    }
};
