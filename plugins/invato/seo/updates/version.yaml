1.0.1:
    - 'Initialize SEO plugin'
    - pre_install_check.php
1.0.2:
    - 'Added SEO fields to Blog\Post'
    - blog_add_seo_fields.php
1.0.3:
    - 'Added SEO field to Invato\Blog'
    - invato_blog_seo_field.php
1.0.4:
    - 'Added SEO field to Invato\Portfolio'
    - invato_portfolio_seo_field.php
1.0.5:
    - 'Added SEO field to Invato\Careers\Vacancy'
    - invato_careers_seo_field.php
1.0.6:
    - 'Added SEO field to Invato\Agenda\Event'
    - invato_agenda_seo_field.php
2.0.0:
    - 'Completely redesigned SEO field data logic'
    - builder_table_create_invato_seo_data.php
2.0.1:
    - 'Migrate SEO data'
    - seo_v2_migrate_agenda_from_v1.php
    - seo_v2_migrate_blog_from_v1.php
    - seo_v2_migrate_careers_from_v1.php
    - seo_v2_migrate_portfolio_from_v1.php
2.0.2:
    - 'Removed old SEO columns from used plugins.'
    - seo_v2_remove_seo_columns_agenda_plugin.php
    - seo_v2_remove_seo_columns_blog_plugin.php
    - seo_v2_remove_seo_columns_careers_plugin.php
    - seo_v2_remove_seo_columns_portfolio_plugin.php
3.0.0:
    - 'Added support for multisite'
    - add_multisite_to_seo_data.php
3.0.1:
    - 'Migrate existing SEO data to multisite'
    - migrate_existing_seo_data_to_multisite.php
