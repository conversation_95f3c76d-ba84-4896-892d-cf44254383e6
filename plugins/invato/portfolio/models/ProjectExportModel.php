<?php

namespace Invato\Portfolio\Models;

use Backend\Models\ExportModel;
use JsonException;

class ProjectExportModel extends ExportModel
{
    /**
     * @throws JsonException
     */
    public function exportData($columns, $sessionKey = null): array
    {
        $projects = Project::with('categories')->get();
        $exportData = [];

        foreach ($projects as $project) {
            $record = [];

            foreach ($columns as $column) {
                // Handle special cases
                match ($column) {
                    'categories' => $record[$column] = $project->categories->pluck('title')->implode(', '),
                    'images' => $record[$column] = $this->formatImagesForExport($project->{$column}),
                    'details' => $record[$column] = $this->formatDetailsForExport($project->{$column}),
                    'custom_options' => $record[$column] = json_encode($project->{$column},
                        JSON_THROW_ON_ERROR),
                    default => $record[$column] = $project->{$column},
                };
            }

            $exportData[] = $record;
        }

        return $exportData;
    }

    /**
     * Format images field for export
     */
    private function formatImagesForExport($images): string
    {
        if (empty($images)) {
            return '';
        }

        if (is_string($images)) {
            return $images;
        }

        if (is_array($images)) {
            return implode(', ', $images);
        }

        return (string) $images;
    }

    /**
     * Format details field for export
     */
    private function formatDetailsForExport($details): string
    {
        if (empty($details)) {
            return '';
        }

        if (is_string($details)) {
            return $details;
        }

        if (is_array($details)) {
            $formatted = [];
            foreach ($details as $detail) {
                if (is_array($detail) && isset($detail['label'], $detail['text'])) {
                    $formatted[] = $detail['label'] . ': ' . $detail['text'];
                } elseif (is_string($detail)) {
                    $formatted[] = $detail;
                }
            }
            return implode('; ', $formatted);
        }

        return (string) $details;
    }
}
