# Handleiding

## Introductie

De Invato Notifications plugin biedt een uitgebreide oplossing voor het beheren van notificaties en popups binnen uw
OCMS Website. Met deze plugin kunt u eenvoudig notificaties en popups aanmaken, bewerken en organiseren.

## Configuratie

Na installatie kunt u de plugin configureren via het backend instellingenpaneel:

1. Ga naar **Instellingen** > **Plugins** > **Notifications**
2. Configureer de volgende opties:
    - Notificaties inschakelen
    - Popups inschakelen
    - Notificatie pagina
    - Popup pagina
    - Maximum aantal notificaties
    - Automatische verberg vertraging

## Notificaties beheren

### Notificatie aanmaken

1. Ga naar **Notifications** > **Notifications**
2. Klik op **Create**
3. Vul de volgende velden in:
    - **Titel**: De titel van de notificatie
    - **Type**: <PERSON><PERSON> tuss<PERSON> default, banner top of banner bottom
    - **Tekst**: De inhou<PERSON> van de notificatie
    - **Stijl**: <PERSON><PERSON> de kleur/stijl (primary, secondary, success, danger, warning, info)
    - **Startdatum**: Wanneer de notificatie actief wordt (optioneel)
    - **Einddatum**: Wanneer de notificatie inactief wordt (optioneel)
    - **Actief**: Of de notificatie momenteel actief is
    - **Melding bij vervaldatum**: Of er een melding gestuurd moet worden bij vervaldatum
    - **Button**: Optionele knop configuratie

### Notificatie bewerken

1. Ga naar **Notifications** > **Notifications**
2. Klik op de notificatie die u wilt bewerken
3. Pas de gewenste velden aan
4. Klik op **Save**

## Popups beheren

### Popup aanmaken

1. Ga naar **Notifications** > **Popups**
2. Klik op **Create**
3. Vul de volgende velden in:
    - **Titel**: De titel van de popup
    - **Tekst**: De inhoud van de popup
    - **Startdatum**: Wanneer de popup actief wordt (optioneel)
    - **Einddatum**: Wanneer de popup inactief wordt (optioneel)
    - **Altijd tonen**: Configuratie voor wanneer de popup getoond wordt
    - **Actief**: Of de popup momenteel actief is
    - **Melding bij vervaldatum**: Of er een melding gestuurd moet worden bij vervaldatum
    - **Button**: Optionele knop configuratie

### Popup bewerken

1. Ga naar **Notifications** > **Popups**
2. Klik op de popup die u wilt bewerken
3. Pas de gewenste velden aan
4. Klik op **Save**

## Import en Export

### Notificaties exporteren

1. Ga naar **Notifications** > **Notifications**
2. Klik op **Export**
3. Selecteer de gewenste kolommen
4. Klik op **Export**

### Notificaties importeren

1. Ga naar **Notifications** > **Notifications**
2. Klik op **Import**
3. Upload uw CSV bestand
4. Map de kolommen correct
5. Klik op **Import**

### Popups exporteren

1. Ga naar **Notifications** > **Popups**
2. Klik op **Export**
3. Selecteer de gewenste kolommen
4. Klik op **Export**

### Popups importeren

1. Ga naar **Notifications** > **Popups**
2. Klik op **Import**
3. Upload uw CSV bestand
4. Map de kolommen correct
5. Klik op **Import**

## Frontend componenten

De plugin biedt vier componenten die u kunt gebruiken op uw frontend pagina's:

1. **Notificationrender**: Toont actieve notificaties
2. **Bannertop**: Toont banner notificaties bovenaan de pagina
3. **Bannerbottom**: Toont banner notificaties onderaan de pagina
4. **Popuprender**: Toont actieve popups

Plaats deze componenten op uw OctoberCMS pagina's en configureer ze naar wens.

## Soft Delete functionaliteit

Beide models (Notifications en Popups) ondersteunen soft delete functionaliteit:

- Verwijderde items worden niet permanent verwijderd
- U kunt verwijderde items bekijken door het "Show deleted" filter aan te zetten
- Verwijderde items kunnen worden hersteld via de restore functie
- Bulk restore is mogelijk via de "Restore selected" knop

## Automatische taken

De plugin bevat een automatische taak die dagelijks om 09:00 controleert op verlopen items en meldingen verstuurt indien
geconfigureerd.
