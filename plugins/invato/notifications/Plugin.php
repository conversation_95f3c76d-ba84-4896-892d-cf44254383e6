<?php

namespace Invato\Notifications;

use Invato\Notifications\Components\Bannerbottom;
use Invato\Notifications\Components\Bannertop;
use Invato\Notifications\Components\Notificationrender;
use Invato\Notifications\Components\Popuprender;
use Invato\Notifications\Console\CheckForExpiredItemsCommand;
use Invato\Notifications\Models\Notification;
use Invato\SiteConfiguration\Models\SiteConfigSettings;
use OFFLINE\Boxes\Models\Box;
use System\Classes\PluginBase;

/**
 * Plugin class
 */
class Plugin extends PluginBase
{

    /**
     * boot method, called right before the request route.
     */
    public function boot(): void
    {
        Box::extend(function ($model) {
            $model->addDynamicMethod('listAllNotifications', function ($value) {
                return Notification::get()->lists('title', 'id');
            });
        });
    }

    /**
     * register method, called when the plugin is first registered.
     */
    public function register(): void
    {
        $this->registerConsoleCommand('notifications.expired-items', CheckForExpiredItemsCommand::class);
    }

    public function registerMailTemplates()
    {
        return [
            'templates' => [
                'invato.notifications::mail.expired-item',
            ],
        ];
    }

    /**
     * registerComponents used by the frontend.
     */
    public function registerComponents()
    {
        return [
            Notificationrender::class => 'notificationrender',
            Bannertop::class => 'bannertop',
            Bannerbottom::class => 'bannerbottom',
            Popuprender::class => 'popuprender',
        ];
    }

    /**
     * registerSettings used by the backend.
     */
    public function registerSettings(): array
    {
        return [];
    }

    public function registerSchedule($schedule)
    {
        if (! empty(SiteConfigSettings::get('company.email'))) {
            $schedule->command('notifications:expired-items')
                ->timezone('Europe/Amsterdam')
                ->dailyAt('09:00');
        }
    }


}
