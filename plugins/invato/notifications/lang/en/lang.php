<?php

return [
    'plugin' => [
        'name' => 'Notifications',
        'description' => 'Invato Notification and Pop-up plugin',
        'notification' => 'Notification',
        'popup' => 'Pop-up',
        'notification_description' => 'Render a Notification',
        'popup_description' => 'Render a Pop-up',
    ],
    'global' => [
        'title' => 'Title',
        'text' => 'Text',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'is_active' => 'Is active',
        'id' => 'ID',
        'created_at' => 'Created at',
        'updated_at' => 'Updated at',
        'deleted_at' => 'Deleted at',
        'notify_on_expiration' => 'Notify on expiration',
        'sort_order' => 'Sort Order',
        'actions' => 'Actions',
        'duplicate' => 'Duplicate',
        'delete' => 'Delete',
        'restore' => 'Restore',
    ],
    'comments' => [
        'is_active' => '',
        'show_always' => '',
        'notify_on_expiration' => 'A day after the end date has passed, an email notification will be sent',
    ],
    'show_always' => [
        'default' => 'Closable and set in cookie',
        'closable' => 'Closable not set in cookie',
        'show_always' => 'Not closable',
    ],
    'notification' => [
        'style' => 'Style',
        'type' => 'Type',
        'description' => 'Choose your notification here',
        'duplicate_confirm' => 'Are you sure you want to duplicate this notification?',
        'duplicate_success' => 'Notification duplicated successfully',
        'restore_confirm' => 'Are you sure you want to restore this notification?',
    ],
    'notification_type' => [
        'default' => 'Default',
        'banner_top' => 'Banner Top',
        'banner_bottom' => 'Banner Bottom',
    ],
    'banner_top' => [
        'description' => 'Render a Banner top',
    ],
    'banner_bottom' => [
        'description' => 'Render a floating Banner bottom',
    ],
    'style' => [
        'primary' => 'Primary',
        'secondary' => 'Secondary',
        'success' => 'Success',
        'danger' => 'Danger',
        'warning' => 'Warning',
        'info' => 'Info',
    ],
    'type' => [
        'default' => 'Default',
        'banner_top' => 'Banner Top',
        'banner_bottom' => 'Banner Bottom',
    ],
    'popup' => [
        'show_always' => 'Show always',
        'description' => 'Choose your pop-up here',
        'duplicate_confirm' => 'Are you sure you want to duplicate this pop-up?',
        'duplicate_success' => 'Pop-up duplicated successfully',
        'restore_confirm' => 'Are you sure you want to restore this pop-up?',
    ],
    'permissions' => [
        'manage_notifications' => 'Manage Notifications',
        'manage_popups' => 'Manage Popups',
        'import_notifications' => 'Import Notifications',
        'export_notifications' => 'Export Notifications',
        'import_popups' => 'Import Popups',
        'export_popups' => 'Export Popups',
    ],
    'menu' => [
        'notifications' => 'Notifications',
        'popups' => 'Popups',
    ],

    'button' => [
        'button' => 'Button',
        'text' => 'Button Text',
        'slug' => 'Link',
        'icon' => 'Icon',
        'icon_placeholder' => 'Choose an icon',
        'icon_position' => 'Icon Position',
        'icon_position_before' => 'Before text',
        'icon_position_after' => 'After text',
        'icon_position_placeholder' => 'Choose an icon position',
        'style' => 'Style',
        'style_rounded' => 'Rounded corners',
        'style_pill' => 'Full rounded corners',
        'style_sharp' => 'Sharp corners',
        'type' => 'Type',
        'type_filled' => 'Filled',
        'type_tonal' => 'Tonal',
        'type_elevated' => 'Elevated',
        'type_outlined' => 'Outlined',
        'type_link' => 'Link',
        'color' => 'Color',
        'color_primary' => 'Primary Color',
        'color_secondary' => 'Secondary Color',
        'color_grayscale' => 'Grayscale',
        'color_error' => 'Error (red)',
        'color_warning' => 'Warning (orange)',
        'color_info' => 'Info (blue)',
        'size' => 'Size',
        'size_sm' => 'Small',
        'size_base' => 'Medium',
        'size_lg' => 'Large',
        'external_link' => 'External Link',
        'external_link_comment' => 'Open link in new tab',
        'extra_css' => 'Extra CSS class',
    ],
];
