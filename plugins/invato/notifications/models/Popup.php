<?php namespace Invato\Notifications\Models;

use Model;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Validation;

/**
 * Popup Model
 */
class Popup extends Model
{
    use SoftDelete;
    use Validation;

    public $implement = [
        \RainLab\Translate\Behaviors\TranslatableModel::class
    ];

    public $translatable = ['title', 'popup', 'button'];

    /**
     * @var string table in the database used by the model.
     */
    public $table = 'invato_notifications_popups';

    // https://laravel.com/docs/10.x/eloquent-mutators#attribute-casting
    protected $casts = [
        'id' => 'integer',
        'title' => 'string',
        'popup' => 'string',
        'start_date' => 'date',
        'end_date' => 'date',
        'show_always' => 'string',
        'is_active' => 'boolean',
        'notify_on_expiration' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'title',
        'popup',
        'button',
        'start_date',
        'end_date',
        'show_always',
        'is_active',
        'notify_on_expiration',
        'sort_order',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * @var array rules for validation.
     */
    public $rules = [
        'title' => 'required|string|max:255',
        'popup' => 'required|string',
        'start_date' => 'nullable|date',
        'end_date' => 'nullable|date|after_or_equal:start_date',
        'show_always' => 'string|max:255',
        'is_active' => 'boolean',
        'notify_on_expiration' => 'boolean',
        'sort_order' => 'integer|min:0',
    ];

    // These attributes should not be in $casts and $rules
    protected $jsonable = [
        'button',
    ];
}
