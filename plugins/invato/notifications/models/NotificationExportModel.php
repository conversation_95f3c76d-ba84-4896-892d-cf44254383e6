<?php

namespace Invato\Notifications\Models;

use Backend\Models\ExportModel;

class NotificationExportModel extends ExportModel
{
    /**
     * @var string table associated with the model
     */
    public $table = 'invato_notifications_notifications';

    /**
     * exportData processes the export data
     */
    public function exportData($columns, $sessionKey = null)
    {
        $notifications = Notification::all();
        $exportData = [];

        foreach ($notifications as $notification) {
            $record = [];

            foreach ($columns as $column) {
                // Handle special cases
                switch ($column) {
                    case 'button':
                        $record[$column] = is_array($notification->button) ? json_encode($notification->button) : $notification->button;
                        break;
                    case 'start_date':
                        $record[$column] = $notification->start_date ? $notification->start_date->format('Y-m-d') : null;
                        break;
                    case 'end_date':
                        $record[$column] = $notification->end_date ? $notification->end_date->format('Y-m-d') : null;
                        break;
                    case 'is_active':
                        $record[$column] = $notification->is_active ? 'Yes' : 'No';
                        break;
                    case 'notify_on_expiration':
                        $record[$column] = $notification->notify_on_expiration ? 'Yes' : 'No';
                        break;
                    default:
                        $record[$column] = $notification->{$column};
                        break;
                }
            }

            $exportData[] = $record;
        }

        return $exportData;
    }
}
