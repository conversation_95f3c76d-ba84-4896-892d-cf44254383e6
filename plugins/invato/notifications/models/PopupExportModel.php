<?php

namespace Invato\Notifications\Models;

use Backend\Models\ExportModel;

class PopupExportModel extends ExportModel
{
    /**
     * @var string table associated with the model
     */
    public $table = 'invato_notifications_popups';

    /**
     * exportData processes the export data
     */
    public function exportData($columns, $sessionKey = null)
    {
        $popups = Popup::all();
        $exportData = [];

        foreach ($popups as $popup) {
            $record = [];

            foreach ($columns as $column) {
                // Handle special cases
                switch ($column) {
                    case 'button':
                        $record[$column] = is_array($popup->button) ? json_encode($popup->button) : $popup->button;
                        break;
                    case 'start_date':
                        $record[$column] = $popup->start_date ? $popup->start_date->format('Y-m-d') : null;
                        break;
                    case 'end_date':
                        $record[$column] = $popup->end_date ? $popup->end_date->format('Y-m-d') : null;
                        break;
                    case 'is_active':
                        $record[$column] = $popup->is_active ? 'Yes' : 'No';
                        break;
                    case 'notify_on_expiration':
                        $record[$column] = $popup->notify_on_expiration ? 'Yes' : 'No';
                        break;
                    default:
                        $record[$column] = $popup->{$column};
                        break;
                }
            }

            $exportData[] = $record;
        }

        return $exportData;
    }
}
