<?php

namespace Invato\Notifications\Models;

use Backend\Models\ImportModel;
use Exception;

class NotificationImportModel extends ImportModel
{
    /**
     * @var array rules for validation
     */
    public $rules = [];

    /**
     * @var string table associated with the model
     */
    public $table = 'invato_notifications_notifications';

    /**
     * importData processes the import data
     */
    public function importData($results, $sessionKey = null)
    {
        foreach ($results as $row => $data) {
            try {
                // Check if notification with this title already exists
                $notification = Notification::withTrashed()->where('title', $data['title'])->first();

                if (! $notification) {
                    $notification = new Notification;
                }

                $notification->fill([
                    'title' => $data['title'] ?? null,
                    'notification' => $data['notification'] ?? null,
                    'style' => $data['style'] ?? 'primary',
                    'type' => $data['type'] ?? 'default',
                    'start_date' => $data['start_date'] ?? null,
                    'end_date' => $data['end_date'] ?? null,
                    'is_active' => $this->convertToBoolean($data['is_active'] ?? false),
                    'notify_on_expiration' => $this->convertToBoolean($data['notify_on_expiration'] ?? false),
                    'sort_order' => $data['sort_order'] ?? 0,
                ]);

                // Handle JSON fields
                if (isset($data['button'])) {
                    $notification->button = is_string($data['button']) ? json_decode($data['button'], true) : $data['button'];
                }

                $notification->save();

                $this->logCreated();
            } catch (Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }

    /**
     * convertToBoolean converts various formats to boolean
     */
    private function convertToBoolean($value)
    {
        if (is_bool($value)) {
            return $value;
        }

        if (is_string($value)) {
            return in_array(strtolower($value), ['1', 'true', 'yes', 'on']);
        }

        return (bool) $value;
    }
}
