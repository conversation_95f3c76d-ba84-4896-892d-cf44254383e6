<?php namespace Invato\Notifications\Models;

use Model;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Validation;

/**
 * Notification Model
 */
class Notification extends Model
{
    use SoftDelete;
    use Validation;

    public $implement = [
        \RainLab\Translate\Behaviors\TranslatableModel::class
    ];

    public $translatable = ['title', 'notification', 'button'];

    /**
     * @var string table in the database used by the model.
     */
    public $table = 'invato_notifications_notifications';

    // https://laravel.com/docs/10.x/eloquent-mutators#attribute-casting
    protected $casts = [
        'id' => 'integer',
        'title' => 'string',
        'notification' => 'string',
        'style' => 'string',
        'type' => 'string',
        'start_date' => 'date',
        'end_date' => 'date',
        'is_active' => 'boolean',
        'notify_on_expiration' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'title',
        'notification',
        'button',
        'style',
        'type',
        'start_date',
        'end_date',
        'is_active',
        'notify_on_expiration',
        'sort_order',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * @var array rules for validation.
     */
    public $rules = [
        'title' => 'required|string|max:255',
        'notification' => 'required|string',
        'style' => 'string|max:255',
        'type' => 'string|max:255',
        'start_date' => 'nullable|date',
        'end_date' => 'nullable|date|after_or_equal:start_date',
        'is_active' => 'boolean',
        'notify_on_expiration' => 'boolean',
        'sort_order' => 'integer|min:0',
    ];

    // These attributes should not be in $casts and $rules
    protected $jsonable = [
        'button',
    ];
}
