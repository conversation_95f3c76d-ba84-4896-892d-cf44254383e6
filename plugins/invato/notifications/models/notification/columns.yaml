columns:
    id:
        label: 'invato.notifications::lang.global.id'
        type: number
        sortable: true
    title:
        label: 'invato.notifications::lang.global.title'
        type: text
        searchable: true
        sortable: true
    start_date:
        label: 'invato.notifications::lang.global.start_date'
        type: date
        sortable: true
    end_date:
        label: 'invato.notifications::lang.global.end_date'
        type: date
        sortable: true
    type:
        label: 'invato.notifications::lang.notification.type'
        type: text
        searchable: true
        sortable: true
    is_active:
        label: 'invato.notifications::lang.global.is_active'
        type: text
        searchable: true
        sortable: true
    notify_on_expiration:
        label: 'invato.notifications::lang.global.notify_on_expiration'
        type: text
        searchable: true
        sortable: true
    created_at:
        label: 'invato.notifications::lang.global.created_at'
        type: date
        sortable: true
        format: d-m-Y
    updated_at:
        label: 'invato.notifications::lang.global.updated_at'
        type: date
        sortable: true
        format: d-m-Y
    # Deleted at only visible when filtering, with trashed is enabled
    deleted_at:
        label: 'invato.notifications::lang.global.deleted_at'
        invisible: true
    actions:
        label: 'invato.notifications::lang.global.actions'
        type: partial
        path: column_actions
        clickable: false
        sortable: false
        align: center
        cssClass: 'column-button'
        width: '110px'
