list: $/invato/notifications/models/notification/columns.yaml
modelClass: Invato\Notifications\Models\Notification
title: Notifications
noRecordsMessage: 'backend::lang.list.no_records'
showSetup: true
showCheckboxes: true
recordsPerPage: 20
toolbar:
    buttons: list_toolbar
    search:
        prompt: 'backend::lang.list.search_prompt'
recordUrl: 'invato/notifications/notifications/update/:id'
structure:
    showTree: false
    showReorder: true
    showSorting: true
# BEGIN Skeleton Soft Deletes
filter: $/invato/notifications/models/notification/scopes.yaml
# END Skeleton Soft Deletes
