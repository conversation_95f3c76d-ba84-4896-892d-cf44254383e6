<?php

namespace Invato\Notifications\Controllers;

use Backend\Classes\Controller;
use BackendMenu;
use File;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Markdown;

class Readme extends Controller
{
    public $requiredPermissions = [
        'superusers.view_readme',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Notifications', 'notifications', 'readme');

        $this->pageTitle = __('Readme');
        $this->viewPath = base_path('/modules/invato/views/pluginmarkdownview');

        // Add CSS and JS for syntax highlighting
        $this->addCss('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css');
        $this->addJs('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js');
        $this->addJs('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/yaml.min.js');
        $this->addJs('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/php.min.js');
        $this->addJs('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/xml.min.js');
        $this->addJs('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/twig.min.js');
    }

    /**
     * @throws FileNotFoundException
     */
    public function index(): void
    {
        $markdownPath = plugins_path('invato/notifications/README.md');

        if (File::exists($markdownPath)) {
            $markdownContent = File::get($markdownPath);
            $this->vars['content'] = Markdown::parse($markdownContent);
        } else {
            $this->vars['content'] = '<p>README.md not found.</p>';
        }
    }
}
