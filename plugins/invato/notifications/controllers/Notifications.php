<?php

namespace Invato\Notifications\Controllers;

use Backend\Behaviors\FormController;
use Backend\Behaviors\ImportExportController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;
use Invato\Notifications\Models\Notification;
use Invato\PluginDuplicateTrait;
use Invato\PluginImportExportTrait;
use Invato\PluginSoftDeleteTrait;

class Notifications extends Controller
{
    use PluginDuplicateTrait;
    use PluginImportExportTrait;
    use PluginSoftDeleteTrait;

    public $implement = [
        FormController::class,
        ListController::class,
        ImportExportController::class,
    ];

    public static $modelClass = Notification::class;

    public $formConfig = 'config_form.yaml';

    public $listConfig = 'config_list.yaml';

    public $importExportConfig = 'config_import_export.yaml';

    public $importPermission = 'invato.notifications.import_notifications';

    public $exportPermission = 'invato.notifications.export_notifications';

    public $requiredPermissions = [
        'invato.notifications.manage_notifications',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Notifications', 'notifications', 'notifications');
    }
}
