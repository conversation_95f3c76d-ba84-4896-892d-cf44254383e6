<?php

namespace Invato\Notifications\Controllers;

use Backend\Behaviors\FormController;
use Backend\Behaviors\ImportExportController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;
use Invato\Notifications\Models\Popup;
use Invato\PluginDuplicateTrait;
use Invato\PluginImportExportTrait;
use Invato\PluginSoftDeleteTrait;

class Popups extends Controller
{
    use PluginDuplicateTrait;
    use PluginImportExportTrait;
    use PluginSoftDeleteTrait;

    public $implement = [
        FormController::class,
        ListController::class,
        ImportExportController::class,
    ];

    public static $modelClass = Popup::class;

    public $formConfig = 'config_form.yaml';

    public $listConfig = 'config_list.yaml';

    public $importExportConfig = 'config_import_export.yaml';

    public $importPermission = 'invato.notifications.import_popups';

    public $exportPermission = 'invato.notifications.export_popups';

    public $requiredPermissions = [
        'invato.notifications.manage_popups',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Notifications', 'notifications', 'popups');
    }
}
