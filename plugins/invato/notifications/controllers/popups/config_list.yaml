list: $/invato/notifications/models/popup/columns.yaml
modelClass: Invato\Notifications\Models\Popup
title: Popups
noRecordsMessage: 'backend::lang.list.no_records'
showSetup: true
showCheckboxes: true
recordsPerPage: 20
toolbar:
    buttons: list_toolbar
    search:
        prompt: 'backend::lang.list.search_prompt'
recordUrl: 'invato/notifications/popups/update/:id'
structure:
    showTree: false
    showReorder: true
    showSorting: true
# BEGIN Skeleton Soft Deletes
filter: $/invato/notifications/models/popup/scopes.yaml
# END Skeleton Soft Deletes
