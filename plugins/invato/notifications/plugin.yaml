plugin:
    name: 'invato.notifications::lang.plugin.name'
    description: 'invato.notifications::lang.plugin.description'
    author: Invato
    icon: oc-icon-exclamation-circle
    homepage: ''
permissions:
    'invato.notifications.manage_notifications':
        tab: 'invato.notifications::lang.plugin.name'
        label: 'invato.notifications::lang.permissions.manage_notifications'
    'invato.notifications.manage_popups':
        tab: 'invato.notifications::lang.plugin.name'
        label: 'invato.notifications::lang.permissions.manage_popups'
    'invato.notifications.import_notifications':
        tab: 'invato.notifications::lang.plugin.name'
        label: 'invato.notifications::lang.permissions.import_notifications'
    'invato.notifications.export_notifications':
        tab: 'invato.notifications::lang.plugin.name'
        label: 'invato.notifications::lang.permissions.export_notifications'
    'invato.notifications.import_popups':
        tab: 'invato.notifications::lang.plugin.name'
        label: 'invato.notifications::lang.permissions.import_popups'
    'invato.notifications.export_popups':
        tab: 'invato.notifications::lang.plugin.name'
        label: 'invato.notifications::lang.permissions.export_popups'
navigation:
    notifications:
        label: 'invato.notifications::lang.plugin.name'
        url: /
        icon: icon-exclamation-circle
        iconSvg: plugins/invato/notifications/assets/images/invato-notifications.svg
        permissions:
            - 'invato.notifications.manage_notifications'
            - 'invato.notifications.manage_popups'
        sideMenu:
            content-section:
                label: 'Content'
                itemType: section

            notifications:
                label: 'invato.notifications::lang.menu.notifications'
                url: invato/notifications/notifications
                icon: icon-exclamation-triangle
                permissions:
                    - 'invato.notifications.manage_notifications'
            popups:
                label: 'invato.notifications::lang.menu.popups'
                url: invato/notifications/popups
                icon: icon-sitemap
                permissions:
                    - 'invato.notifications.manage_popups'

            documentation-section:
                label: 'Documentation'
                itemType: section

            readme:
                label: 'Readme'
                url: invato/notifications/readme
                icon: icon-book
                permissions:
                    - 'superusers.view_readme'
            manual:
                label: 'Manual'
                url: invato/notifications/manual
                icon: icon-book
                permissions:
                    - 'invato.notifications.manage_notifications'
