# Invato Redirects Plugin - Gebruikershandleiding

## Inhoudsopgave

1. [Inleiding](#inleiding)
2. [A<PERSON> de slag](#aan-de-slag)
3. [Redirects beheren](#redirects-beheren)
4. [Import/Export](#importexport)
5. [Permissies](#permissies)
6. [Probleemoplossing](#probleemoplossing)

## Inleiding

De Invato Redirects Plugin stelt je in staat om URL redirects te beheren in je OCMS website. Dit is essentieel voor het
behouden van SEO rankings wanneer URLs veranderen en voor het bieden van een betere gebruikerservaring.

## Aan de slag

### Toegang tot de Plugin

1. Log in op je OCMS backend
2. Navigeer naar **Redirects** in het hoofdmenu
3. Klik op **Redirects** om de redirect beheerinterface te bekijken

### Redirect Types Begrijpen

- **301 Redirect**: Permanente redirect (aanbevolen voor SEO)
- **302 Redirect**: Tijdelijke redirect

## Redirects beheren

### Een nieuwe Redirect aanmaken

1. Klik op de **Aanmaken** knop in de redirects lijst
2. Vul de vereiste velden in:
    - **Oude URL**: De URL waarvan je wilt redirecten
    - **Nieuwe URL**: De bestemmings URL
    - **Status**: Kies 301 (permanent) of 302 (tijdelijk)
    - **Notitie**: Optionele beschrijving voor interne referentie

3. Klik **Opslaan** om de redirect aan te maken

### Redirects bewerken

1. Klik op een redirect in de lijst om deze te bewerken
2. Wijzig de velden naar behoefte
3. Klik **Opslaan** om wijzigingen toe te passen

### Redirects verwijderen

1. Selecteer een of meer redirects met de checkboxes
2. Klik op de **Geselecteerde verwijderen** knop
3. Bevestig de verwijdering

### Verwijderde Redirects herstellen

1. Schakel het **Toon verwijderde** filter in de lijst in
2. Selecteer verwijderde redirects
3. Klik op de **Geselecteerde herstellen** knop

## Import/Export

### Redirects exporteren

1. Klik op de **Exporteren** knop in de toolbar
2. Kies je export formaat (CSV)
3. Download het gegenereerde bestand

### Redirects importeren

1. Klik op de **Importeren** knop in de toolbar
2. Upload je CSV bestand
3. Koppel de kolommen aan de juiste velden
4. Controleer en bevestig de import

### CSV Formaat

Je CSV bestand moet deze kolommen bevatten:

- `old_url`: De bron URL
- `new_url`: De bestemmings URL
- `status`: HTTP status code (301 of 302)
- `comment`: Optionele beschrijving

## Permissies

De plugin gebruikt het volgende permissie systeem:

### Beschikbare Permissies

- **Beheer Redirects** (`invato.redirects.manage_redirects`)
    - Aanmaken, bewerken, verwijderen van redirects
    - Bekijken van redirect lijst

- **Importeer Redirects** (`invato.redirects.import_redirects`)
    - Uploaden en importeren van redirect data

- **Exporteer Redirects** (`invato.redirects.export_redirects`)
    - Downloaden van redirect data

### Permissies toewijzen

1. Dit gaat via Invato. neem contact op met Invato support indien je meer permissies nodig hebt.

## Probleemoplossing

### Veelvoorkomende Problemen

**Redirect werkt niet:**

- Controleer dat de oude URL exact correct is
- Zorg ervoor dat de redirect status juist is (301/302)
- Verifieer dat de nieuwe URL toegankelijk is

**Import mislukt:**

- Controleer of CSV formaat voldoet aan vereisten
- Zorg ervoor dat alle vereiste velden aanwezig zijn
- Verifieer dat bestandscodering UTF-8 is

**Toegang geweigerd:**

- Controleer of gebruiker juiste permissies heeft
- Neem contact op met beheerder voor juiste rol

### Hulp krijgen

Als je problemen tegenkomt die niet in deze handleiding staan:

1. Controleer de systeem logs in **Instellingen** > **Systeem** > **Event Log**
2. Neem contact op met je systeembeheerder
3. Raadpleeg de plugin README voor technische details

## Beste Praktijken

### SEO Overwegingen

- Gebruik 301 redirects voor permanente URL wijzigingen
- Gebruik 302 redirects alleen voor tijdelijke wijzigingen
- Vermijd redirect ketens (A→B→C)
- Test redirects na implementatie

### Onderhoud

- Controleer en ruim regelmatig oude redirects op
- Gebruik notities om te documenteren waarom redirects zijn aangemaakt
- Exporteer redirects als backup voor grote wijzigingen
- Monitor redirect prestaties en update indien nodig
