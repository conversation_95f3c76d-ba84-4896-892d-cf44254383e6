plugin:
    name: 'invato.redirects::lang.plugin.name'
    description: 'invato.redirects::lang.plugin.description'
    author: Invato
    icon: oc-icon-link
    homepage: ''
permissions:
    'invato.redirects.manage_redirects':
        tab: 'invato.redirects::lang.plugin.name'
        label: 'invato.redirects::lang.permissions.manage_redirects'
    'invato.redirects.import_redirects':
        tab: 'invato.redirects::lang.plugin.name'
        label: 'invato.redirects::lang.permissions.import_redirects'
    'invato.redirects.export_redirects':
        tab: 'invato.redirects::lang.plugin.name'
        label: 'invato.redirects::lang.permissions.export_redirects'
navigation:
    redirects:
        label: 'invato.redirects::lang.plugin.name'
        url: /
        icon: icon-link
        iconSvg: plugins/invato/redirects/assets/images/invato-redirects.svg
        permissions:
            - 'invato.redirects.manage_redirects'
        sideMenu:
            content-section:
                label: 'invato.redirects::lang.menu.content'
                itemType: section

            redirects:
                label: 'invato.redirects::lang.menu.redirects'
                url: invato/redirects/redirects
                icon: icon-random
                permissions:
                    - 'invato.redirects.manage_redirects'

            documentation-section:
                label: 'Documentation'
                itemType: section

            readme:
                label: 'Readme'
                url: invato/redirects/readme
                icon: icon-book
                permissions:
                    - 'superusers.view_readme'
            manual:
                label: 'Manual'
                url: invato/redirects/manual
                icon: icon-book
                permissions:
                    - 'invato.redirects.manage_redirects'
