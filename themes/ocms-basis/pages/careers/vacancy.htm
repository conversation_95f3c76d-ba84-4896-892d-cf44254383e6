url = "/vacatures/:slug"
layout = "default"
title = "Vacature"

[CareersVacancyList]

[CareersVacancyDetail]
slug = "{{ :slug }}"
==
<?php

    function onEnd()
    {
        use Invato\SiteConfiguration\Models\SiteConfigSettings;
        $vacancy = $this->components['CareersVacancyDetail'];
        $vacancyTitle = $vacancy->vacancy?->title;
        $vacancyMetaDesc = $vacancy->vacancy?->seo['meta_description'];
        $vacancyOgDesc = $vacancy->vacancy?->seo['og_description'];

        if (
            isset($vacancy->vacancy?->seo['meta_title'])
            &&
            trim($vacancy->vacancy?->seo['meta_title']) !== ''
        ) {
            $seoTitle = $vacancy->vacancy?->seo['meta_title'];
        }

        $seoDesc = $vacancyMetaDesc;
        if (
            isset($vacancyOgDesc)
            &&
            trim($vacancyOgDesc) !== ''
        ) {
            $seoDesc = $vacancyOgDesc;
        }
        if (
            isset($vacancyMetaDesc)
            &&
            trim($vacancyMetaDesc) !== ''
        ) {
            $seoDesc = $vacancyMetaDesc;
        }

        $seoTitleSuffix = SiteConfigSettings::get('meta_title_suffix');
        $title = $seoTitle ?? $vacancyTitle;
        $this->page->title = $title . $seoTitleSuffix;
        $this->page->meta_description = $seoDesc;
    }

?>
==
{% if not CareersVacancyDetail %}
    {% do abort(404) %}
{% endif %}
{% set vacancy = CareersVacancyDetail.vacancy %}
{% set careersPage = CareersVacancyDetail.careersPage %}
{% set vacancyPage = CareersVacancyDetail.vacancyPage %}

{% if vacancy is empty %}
{% do abort(404) %}
{% endif %}

{% put adminbar %}
    {% partial 'atomic/atoms/admin-bar-link' link="/backend/invato/careers/vacancies/update/" id=vacancy.id label="Vacature bewerken" icon="fa-pen-to-square" %}
{% endput %}

<div class="bg-gray-200 py-8 md:py-16">
    <div class="container">
        <span class="text-base font-semibold tracking-wide uppercase">{{ 'Vacature'|_ }}</span>
        <h1 class="mb-0">{{ vacancy.title }}</h1>
    </div>
</div>

<div class="bg-gray-100 py-8 md:py-16">
    <div class="container">
        <div class="max-w-4xl mb-12">
            <h2 class="text-gray-900 font-extrabold mb-4">{{ 'Over deze vacature'|_ }}</h2>
            <div class="text-gray-400 border-y border-gray-300 py-2 flex justify-between mb-8">
                <div class=""><i class="fa-solid fa-list-check flex-shrink-0 mr-2"></i>{{ vacancy.tasks }}</div>
                <div class="flex space-x-8">
                    <div class=""><i class="fa-solid fa-location-dot flex-shrink-0 mr-2"></i>{{ vacancy.location }}</div>
                    <div class=""><i class=" flex-shrink-0 mr-2 fa-solid fa-clock"></i>{{ vacancy.hours }}</div>
                </div>
            </div>
            <div class="content_section">{{ vacancy.description_top | content }}</div>
        </div>
    </div>
</div>

<div class="pb-8 md:pb-16">
    <div class="container">
        <div class="md:grid md:grid-cols-2 md:gap-16 -translate-y-12">
            <div class="bg-white rounded-xl shadow-xl p-8">
                <div class="flex items-center">
                    <div class="flex h-12 w-12 items-center justify-center rounded-md bg-primary-600 text-white">
                        <div class="w-6 h-6 flex flex-wrap items-center justify-center text-white">
                            <i class="fa-regular fa-award text-2xl"></i>
                        </div>
                    </div>
                    <h3 class="text-gray-900 leading-none ml-6 mb-0">{{ 'Jouw vaardigheden'|_ }}</h3>
                </div>
                <div class="mt-8">
                    <ul role="list" class="divide-y divide-gray-200" itemprop="qualifications">
                        {% for requirement in vacancy.requirements %}
                        <li class="py-4 flex {% if loop.first %}md:py-0 md:pb-4{% endif %}">
                            <i class="fa-sharp fa-regular fa-check flex-shrink-0 h-6 w-6 text-green-500"></i>
                            <span class="ml-3 text-base text-gray-500">{{ requirement.requirements }}</span>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-xl p-8">
                <div class="flex items-center">
                    <div class="flex h-12 w-12 items-center justify-center rounded-md bg-primary-600 text-white">
                        <div class="w-6 h-6 flex flex-wrap items-center justify-center text-white">
                            <i class="fa-solid fa-sack-dollar text-2xl"></i>
                        </div>
                    </div>
                    <h3 class="text-gray-900 leading-none ml-6 mb-0">{{ 'Wat wij bieden'|_ }}</h3>
                </div>
                <div class="mt-8">
                    <ul role="list" class="divide-y divide-gray-200" itemprop="qualifications">
                        {% for offer in vacancy.offer %}
                        <li class="py-4 flex {% if loop.first %}md:py-0 md:pb-4{% endif %}">
                            <i class="fa-sharp fa-regular fa-check flex-shrink-0 h-6 w-6 text-green-500"></i>
                            <span class="ml-3 text-base text-gray-500">{{ offer.offer }}</span>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>

        <div class="max-w-4xl mb-12">
            <div class="content_section">{{ vacancy.description_bottom | content }}</div>
        </div>
        <div class="careers-button pb-4 md:pb-8" x-data="openClose">
            {% set btnText = "Solliciteer direct!"|_ %}
            {% partial 'ui/button_html' text=btnText color="primary" style="rounded" openmodal="true" %}

            {% partial 'ui/modal' body %}
            <div class="flex justify-between w-full mb-9">
                <div class="">
                    <h3>{{ 'Solliciteren voor'|_ }} {{ vacancy.title }}</h3>
                    <p>{{ 'Vul het onderstaande formulier in om te solliciteren.'|_ }}</p>
                </div>
                <div class="pl-4">
                    <button type="button" x-on:click="open = false"
                            class="">
                        <i class="text-lg fa-regular fa-square-xmark text-secondary-400 hover:text-primary-500"></i>
                    </button>
                </div>
            </div>
            {% set vacancyTitle = vacancy.title | replace({'\'':''}) %}
            <div class="" x-data="{ applyFor: '{{ vacancyTitle }}' }" data-vacancy="{{ vacancy.title }}">
                {% ajaxPartial 'site/dynamic-form' formcode="solliciteren" %}
            </div>
            {% endpartial %}
        </div>
    </div>
</div>

{# Add SEO meta tags to page #}
{% set seo_object = vacancy %}
{% partial 'site/seo_meta' item = seo_object %}
