url = "/cv-ketels/:merk*/:slug"
layout = "boxes"
title = "CV Ketel"

[ketelDetail]
slug = "{{ :slug }}"

[renderForm ketelformrender]
formCode = "ketel-prijs-aanvraag"
==
<?php

    function onEnd()
    {
        use Invato\SiteConfiguration\Models\SiteConfigSettings;
        $post = $this->components['ketelDetail'];
        $postTitle = $post->boiler?->title;
        $postMetaDesc = $post->boiler?->seo['meta_description'];
        $postOgDesc = $post->boiler?->seo['og_description'];
        $postShortDesc = $post->boiler?->short_description;

        if (
            isset($post->boiler?->seo['meta_title'])
            &&
            trim($post->boiler?->seo['meta_title']) !== ''
        ) {
            $seoTitle = $post->boiler?->seo['meta_title'];
        }

        $seoDesc = $postShortDesc;
        if (
            isset($postOgDesc)
            &&
            trim($postOgDesc) !== ''
        ) {
            $seoDesc = $postOgDesc;
        }
        if (
            isset($postMetaDesc)
            &&
            trim($postMetaDesc) !== ''
        ) {
            $seoDesc = $postMetaDesc;
        }

        $seoTitleSuffix = SiteConfigSettings::get('meta_title_suffix');
        $title = $seoTitle ?? $postTitle;
        $this->page->title = $title . $seoTitleSuffix;
        $this->page->meta_description = $seoDesc;
    }

?>
==
{% if not ketelDetail %}
    {% do abort(404) %}
{% endif %}

{% put adminbar %}
    {% partial 'atomic/atoms/admin-bar-link' link="/backend/instalweb/ketels/boilers/update/" id=ketelDetail.boiler.id label="Ketel bewerken" icon="fa-pen-to-square" %}
{% endput %}

{% component 'ketelDetail' %}

{# Add SEO meta tags to page #}
{% set seo_object = ketelDetail.boiler %}
{% partial 'site/seo_meta' item = seo_object %}
