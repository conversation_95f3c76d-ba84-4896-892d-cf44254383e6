url = "/cv-ketels/:slug"
layout = "boxes"
title = "CV Ketel merk"

[brandList]
slug = "{{ :slug }}"

[siteSearchInclude]
==
<?php

    function onEnd()
    {
        use Invato\SiteConfiguration\Models\SiteConfigSettings;
        $post = $this->components['brandList'];
        $postTitle = $post->brand?->title;
        $postMetaDesc = $post->brand?->seo['meta_description'];
        $postOgDesc = $post->brand?->seo['og_description'];
        $postShortDesc = strip_tags($post->brand?->description);

        if (
            isset($post->brand?->seo['meta_title'])
            &&
            trim($post->brand?->seo['meta_title']) !== ''
        ) {
            $seoTitle = $post->brand?->seo['meta_title'];
        }

        $seoDesc = $postShortDesc;
        if (
            isset($postOgDesc)
            &&
            trim($postOgDesc) !== ''
        ) {
            $seoDesc = $postOgDesc;
        }
        if (
            isset($postMetaDesc)
            &&
            trim($postMetaDesc) !== ''
        ) {
            $seoDesc = $postMetaDesc;
        }

        $seoTitleSuffix = SiteConfigSettings::get('meta_title_suffix');
        $title = $seoTitle ?? $postTitle;
        $this->page->title = $title . $seoTitleSuffix;
        $this->page->meta_description = $seoDesc;
    }

?>
==
{% if not brandList %}
    {% do abort(404) %}
{% endif %}

{% put adminbar %}
    {% partial 'atomic/atoms/admin-bar-link' link="/backend/instalweb/ketels/brands/update/" id=brandList.brand.id label="Merk bewerken" icon="fa-pen-to-square" %}
{% endput %}

{% component 'brandList' %}

{# Add SEO meta tags to page #}
{% set seo_object = brandList.brand %}
{% partial 'site/seo_meta' item = seo_object %}
