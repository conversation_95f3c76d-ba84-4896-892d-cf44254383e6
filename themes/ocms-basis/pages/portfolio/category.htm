url = "/portfolio/categorie/:slug"
layout = "boxes"
title = "Portfolio categorie"

[PortfolioCategoryDetail]
slug = "{{ :slug }}"
itemsPerPage = 8

[PortfolioCategoryList]

[PortfolioProjectList]
==
<?php

    function onEnd()
    {
        use Invato\SiteConfiguration\Models\SiteConfigSettings;
        $cat = $this->components['PortfolioCategoryDetail'];
        $catTitle = $cat->category?->title;
        $catMetaDesc = $cat->category?->seo['meta_description'];
        $catOgDesc = $cat->category?->seo['og_description'];

        if (
            isset($cat->category?->seo['meta_title'])
            &&
            trim($cat->category?->seo['meta_title']) !== ''
        ) {
            $seoTitle = $cat->category?->seo['meta_title'];
        }

        $seoDesc = $catMetaDesc;
        if (
            isset($catOgDesc)
            &&
            trim($catOgDesc) !== ''
        ) {
            $seoDesc = $catOgDesc;
        }
        if (
            isset($catMetaDesc)
            &&
            trim($catMetaDesc) !== ''
        ) {
            $seoDesc = $catMetaDesc;
        }

        $seoTitleSuffix = SiteConfigSettings::get('meta_title_suffix');
        $title = $seoTitle ?? $catTitle;
        $this->page->title = $title . $seoTitleSuffix;
        $this->page->meta_description = $seoDesc;
    }

?>
==
{% set items = PortfolioCategoryDetail.projects %}
{% set slug = PortfolioCategoryDetail.slug %}
{% set categories = PortfolioCategoryList.categories %}
{% set projectPage = PortfolioProjectList.projectPage %}
{% set portfolioPage = PortfolioProjectList.portfolioPage|link %}

{% set dark = '' %}

{% put adminbar %}
    {% partial 'atomic/atoms/admin-bar-link' link="/backend/invato/portfolio/categories/update/" id=category.id label="Categorie bewerken" icon="fa-pen-to-square" %}
{% endput %}

<section
    data-name="portfolio-category"
    data-category="portfolio"
    class="{{ dark }} dark:bg-gray-700">

    <div class="py-2 border-b">
        <div class="container">
            {% partial 'atomic/atoms/portfolio/portfolio-breadcrumbs' portfolioPage=portfolioPage %}
        </div>
    </div>

    <div class="container space-y-8 lg:space-y-16 py-8 lg:pt-4 lg:pb-16">

        <div class="content-centered max-w-4xl mx-auto text-center">

            <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                <div class="content-centered-heading">
                    {% set subTitle = 'Portfolio Categorie'|_ %}
                    {% partial 'atomic/atoms/headings/header-h4' text=subTitle %}

                    {% partial 'atomic/atoms/headings/header-h1' text=category.title %}
                </div>

                <div class="md:px-16 lg:px-24">
                    {% partial 'atomic/molecules/content-section' content=category.description %}
                </div>
            </div>
        </div>

        <div class="flex items-center justify-center">
            <div class="flex flex-wrap items-center justify-center dark:bg-gray-300 border border-gray-400 rounded-lg p-1 gap-2">
                {% if this.param.slug == item.slug %}
                    {% set activeStyle = 'bg-primary-100 hover:bg-primary-200 text-primary-900 dark:bg-primary-600 dark:hover:bg-primary-700 dark:text-primary-50' %}
                {% else %}
                    {% set activeStyle = 'bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 dark:text-gray-100' %}
                {% endif %}

                {% set linkTitle = 'Alle projecten'|_ %}
                <a href="{{ portfolioPage }}" title="{{ item.title }}"
                   class="inline-block {{ activeStyle }} font-bold tracking-wide text-sm rounded-lg shadow-sm leading-none py-3 px-6">
                    {{ linkTitle }}
                </a>
                {% for item in categories %}

                    {% if this.param.slug == item.slug %}
                        {% set activeStyle = 'bg-primary-100 hover:bg-primary-200 text-primary-900 dark:bg-primary-600 dark:hover:bg-primary-700 dark:text-primary-50' %}
                    {% else %}
                        {% set activeStyle = 'bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 dark:text-gray-100' %}
                    {% endif %}
                    <a href="{{ categoryPage | page({ slug: item.slug }) }}" title="{{ item.title }}"
                       class="inline-block {{ activeStyle }} font-bold tracking-wide text-sm rounded-lg shadow-sm leading-none py-3 px-6">
                        {{ item.title }}
                    </a>

                {% endfor %}
            </div>
        </div>

        {% if PortfolioCategoryDetail.projects.items %}

            <div class="space-y-8 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-4 md:gap-8">
                {% for item in items %}
                    {% partial 'atomic/molecules/portfolio/project' item=item projectPage=projectPage card_size = card_size %}
                {% endfor %}
            </div>

        {% else %}

            <div class="flex items-center justify-center w-full">
                <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                    {% set emptyText = 'Sorry! Geen portfolio projecten gevonden in deze categorie.'|_ %}
                    {% partial 'atomic/atoms/headings/header-h3' text=emptyText %}
                </div>
            </div>

        {% endif %}

    </div>

    <div class="">
        {{ pager(items, { partial: 'ui/pagination' }) }}
    </div>

</section>

{# Add SEO meta tags to page #}
{% set seo_object = vacancy %}
{% partial 'site/seo_meta' item = seo_object %}
