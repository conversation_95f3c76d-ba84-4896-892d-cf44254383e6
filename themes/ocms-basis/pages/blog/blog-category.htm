url = "/blog/category/:slug"
layout = "boxes"
title = "Blog categorieën"

[categoryDetail]
slug = "{{ :slug }}"
itemsPerPage = 8

[categoryList]

[postList]

[siteSearchInclude]
==
<?php

    function onEnd()
    {
        use Invato\SiteConfiguration\Models\SiteConfigSettings;
        $cat = $this->components['categoryDetail'];
        $catTitle = $cat->category?->title;
        $catMetaDesc = $cat->category?->seo['meta_description'];
        $catOgDesc = $cat->category?->seo['og_description'];

        if (
        isset($cat->category?->seo['meta_title'])
        &&
        trim($cat->category?->seo['meta_title']) !== ''
        ) {
        $seoTitle = $cat->category?->seo['meta_title'];
        }

        $seoDesc = $catMetaDesc;
        if (
            isset($catOgDesc)
            &&
            trim($catOgDesc) !== ''
        ) {
            $seoDesc = $catOgDesc;
        }
        if (
            isset($catMetaDesc)
            &&
            trim($catMetaDesc) !== ''
        ) {
            $seoDesc = $catMetaDesc;
        }

        $seoTitleSuffix = SiteConfigSettings::get('meta_title_suffix');
        $title = $seoTitle ?? $catTitle;
        $this->page->title = $title . $seoTitleSuffix;
        $this->page->meta_description = $seoDesc;
    }

?>
==

{% set items = categoryDetail.posts %}
{% set slug = categoryDetail.slug %}
{% set categories = categoryList.categories %}
{% set postPage = postList.postPage %}
{% set blogPage = postList.blogPage|link %}

{% set featuredItems = items|filter(item => attribute(item, 'is_featured') == 1) %}
{% set allNonFeaturedItems = items|filter(item => attribute(item, 'is_featured') == 0) %}

{% set dark = '' %}

{% put adminbar %}
    {% partial 'atomic/atoms/admin-bar-link' link="/backend/invato/blog/categories/update/" id=category.id label="Categorie bewerken" icon="fa-pen-to-square" %}
{% endput %}


<section
    data-name="blog-categories"
    data-category="blog"
    class="{{ dark }} dark:bg-gray-700">

    <div class="py-2 border-b">
        <div class="container">
            {% partial 'atomic/atoms/blog/blog-breadcrumbs' blogPage=blogPage %}
        </div>
    </div>

    <div class="container space-y-8 lg:space-y-16 py-8 lg:pt-4 lg:pb-16">


        <div class="content-centered max-w-4xl mx-auto text-center">

            <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                <div class="content-centered-heading">
                    {% set subTitle = 'Blog Categorie'|_ %}
                    {% partial 'atomic/atoms/headings/header-h4' text=subTitle %}

                    {% partial 'atomic/atoms/headings/header-h1' text=category.title %}
                </div>

                <div class="md:px-16 lg:px-24">
                    {% partial 'atomic/molecules/content-section' content=category.description %}
                </div>
            </div>
        </div>

        <div class="flex items-center justify-center">
            <div class="flex flex-wrap items-center justify-center dark:bg-gray-300 border border-gray-400 rounded-lg p-1 gap-2">
                {% if this.param.slug == item.slug %}
                    {% set activeStyle = 'bg-primary-100 hover:bg-primary-200 text-primary-900 dark:bg-primary-600 dark:hover:bg-primary-700 dark:text-primary-50' %}
                {% else %}
                    {% set activeStyle = 'bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 dark:text-gray-100' %}
                {% endif %}

                {% set linkTitle = 'Alle berichten'|_ %}
                <a href="{{ blogPage }}" title="{{ item.title }}"
                   class="inline-block {{ activeStyle }} font-bold tracking-wide text-sm rounded-lg shadow-sm leading-none py-3 px-6">
                    {{ linkTitle }}
                </a>
                {% for item in categories %}

                    {% if this.param.slug == item.slug %}
                        {% set activeStyle = 'bg-primary-100 hover:bg-primary-200 text-primary-900 dark:bg-primary-600 dark:hover:bg-primary-700 dark:text-primary-50' %}
                    {% else %}
                        {% set activeStyle = 'bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 dark:text-gray-100' %}
                    {% endif %}
                    <a href="{{ categoryPage | page({ slug: item.slug }) }}" title="{{ item.title }}"
                       class="inline-block {{ activeStyle }} font-bold tracking-wide text-sm rounded-lg shadow-sm leading-none py-3 px-6">
                        {{ item.title }}
                    </a>

                {% endfor %}
            </div>
        </div>

        {% if categoryDetail.posts.items %}

            <div class="max-w-2xl mx-auto grid gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 lg:max-w-none">
                {% for item in featuredItems %}
                    {% partial 'atomic/molecules/blog/blog-item-card-large' item=item categoryPage=categoryPage link=postPage | page({ slug: item.slug }) %}
                {% endfor %}
                {% for item in allNonFeaturedItems %}
                    {% partial 'atomic/molecules/blog/blog-item-card' item=item categoryPage=categoryPage postPage=postPage dark=dark %}
                {% endfor %}
            </div>

        {% elseif items == '' %}

            <div class="flex items-center justify-center w-full">
                <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                    {% set text = 'Sorry! Geen blog items gevonden in deze categorie.'|_ %}
                    {% partial 'atomic/atoms/headings/header-h3' text=text %}
                </div>
            </div>

        {% endif %}

    </div>

    <div class="">
        {{ pager(items, { partial: 'ui/pagination' }) }}
    </div>

</section>


{# Add SEO meta tags to page #}
{% set seo_object = category %}
{% partial 'site/seo_meta' item = seo_object %}
