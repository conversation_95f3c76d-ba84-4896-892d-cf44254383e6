url = "/agenda/evenement/:slug"
layout = "boxes"
title = "Evenement"

[EventDetail]
slug = "{{ :slug }}"

[EventFeed]
maxItems = 5
==
<?php

    function onEnd()
    {
        use Invato\SiteConfiguration\Models\SiteConfigSettings;
        $event = $this->components['EventDetail'];
        $eventTitle = $event->event?->title;

        if (
            isset($event->event?->seo['meta_title'])
            &&
            trim($event->event?->seo['meta_title']) !== ''
        ) {
            $seoTitle = $event->event?->seo['meta_title'];
        }

        $seoTitleSuffix = SiteConfigSettings::get('meta_title_suffix');
        $title = $seoTitle ?? $eventTitle;
        $this->page->title = $title . $seoTitleSuffix;
    }

?>
==
{% if not EventDetail %}
    {% do abort(404) %}
{% endif %}

{% put adminbar %}
    {% partial 'atomic/atoms/admin-bar-link' link="/backend/invato/agenda/events/update/" id=event.id label="Evenement bewerken" icon="fa-pen-to-square" %}
{% endput %}


<div class="py-8 md:py-16">
    <div class="container">
        <div class="md:grid md:grid-cols-12 md:gap-12">
            <div class="col-span-8">
                <h1>{{ event.title }}</h1>
                <div class="mt-8">
                    <div class="text-primary-600 text-2xl font-semibold rounded-lg">{{ carbon(event.date).format('d F Y') }}</div>
                </div>
                <div class="text-xl text-gray-700 font-medium mt-8">
                    {{ event.excerpt }}
                </div>
                <div class="mt-8 py-8 border-t content_section">
                    {{ event.text | content }}
                </div>
            </div>
            <div class="col-span-4">
                <div class="aspect-thumb">
                    <img src="{{ event.overview_image | media | resize(600) }}" alt="{{ event.title }}" class="rounded-xl shadow-xl w-full h-full object-cover">
                </div>
                <div class="mt-9 bg-primary-100 rounded-xl p-8">
                    <table class="w-full text-left text-xl align-top">
                        <tr>
                            <th class="p-2 align-top">{{ 'Datum'|_ }}:</th>
                            <td class="p-2 font-medium">{{ event.date | date('d-m-Y') }}</td>
                        </tr>
                        {% if event.time_start %}
                            <tr>
                                <th class="p-2 align-top">{{ 'Begintijd'|_ }}:</th>
                                <td class="p-2 font-medium">{{ event.time_start | date('H:i') }}</td>
                            </tr>
                        {% endif %}
                        {% if event.time_end %}
                            <tr>
                                <th class="p-2 align-top">{{ 'Eindtijd'|_ }}:</th>
                                <td class="p-2 font-medium">{{ event.time_end | date('H:i') }}</td>
                            </tr>
                        {% endif %}
                        {% if event.location %}
                            <tr>
                                <th class="p-2 align-top">{{ 'Locatie'|_ }}:</th>
                                <td class="p-2 font-medium">{{ event.location }}</td>
                            </tr>
                        {% endif %}
                    </table>
                </div>

                <div class="mt-8">
                    {% set buttonText = "Terug naar overzicht"|_ %}
                    {% partial "ui/button" text=buttonText size="sm" style="rounded" type="outlined" color="grayscale" %}
                </div>
            </div>
        </div>
    </div>
</div>
<div class="py-8 md:py-16 bg-gray-100">
    <div class="container">
        <h2>{{ 'Meer evenementen'|_ }}</h2>
        <div class="mt-16">
            <div>
                {% if agendaPage %}
                    {% for item in EventFeed.events %}
                        {% partial 'boxes/agenda/includes/event-item' item = item %}
                    {% endfor %}
                {% else %}
                    {% if adminLoggedIn %}
                        <h4 class="text-red-500">Geen agenda pagina ingesteld</h4>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
</div>

{# Add SEO meta tags to page #}
{% set seo_object = event %}
{% partial 'site/seo_meta' item = seo_object %}
