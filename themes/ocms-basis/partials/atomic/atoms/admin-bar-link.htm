<div class="py-3 px-4">
    <a href="{{ url(link ~ id) }}" target="_blank" class="text-gray-600 text-sm hover:text-black group">
        <i class="text-base text-primary {{ iconStyle|default('fa-solid') }} {{ icon }} group-hover:text-primary-hover transition"></i>
        <span class="pl-2 whitespace-nowrap overflow-hidden"
            x-show="open"
            x-cloak
            x-transition:enter="transition-all duration-300 ease-out"
            x-transition:enter-start="opacity-0 w-0"
            x-transition:enter-end="opacity-100 w-full"
            x-transition:leave="transition-all duration-150 ease-out"
            x-transition:leave-start="opacity-100 w-full"
            x-transition:leave-end="opacity-0 w-0">
            {{ label }}
        </span>
    </a>
</div>
