==
<?php
    use Illuminate\Support\Facades\Artisan;
    use Backend\Facades\BackendAuth;

    function onStart()
    {
        $this['isLoggedIntoBackend'] = BackendAuth::check();
        $this['isSuperUser'] = BackendAuth::user()?->is_superuser;
        $this['isBackend'] = Request::is('__boxes-preview/*');
    }

    function onEmptyCache()
    {
        Artisan::call('optimize:clear');
        Artisan::call('october:optimize');
        return true;
    }
    function onMigrateDatabase()
    {
        Artisan::call('october:migrate');
        return true;
    }
?>
==

{% set barPosition = "left-4 top-1/2 -translate-y-1/2" %}
{% set barList = "divide-y divide-black/20" %}
{% set barStyle = "bg-white/80 backdrop-blur-sm border border-black/20 shadow-lg rounded-lg" %}

{% if isLoggedIntoBackend and not isBackend %}
<div id="adminbar" class="hidden lg:block fixed {{ barPosition }} z-[999]" x-data="{ open: false }">
    <div class="{{ barStyle }}">
        <div class="{{ barList }}" x-on:mouseenter="open = true" x-on:mouseleave="open = false">
            {% partial 'atomic/atoms/admin-bar-link' link="/backend" label="Beheerderspaneel" iconStyle="fa-kit" icon="fa-ocms" %}

            {% if boxesPage %}
                {% partial 'atomic/atoms/admin-bar-link' link="/backend/offline/boxes/editorcontroller?boxes_page=" id=boxesPage.id label="Pagina bewerken" icon="fa-pen-to-square" %}
            {% endif %}

            {% placeholder adminbar %}

            {% partial 'atomic/atoms/admin-bar-link' link="/backend/system/settings/update/invato/siteconfiguration/settings" label="Bedrijfsgegevens bewerken" icon="fa-id-card" %}

            {% if isSuperUser %}
                <div class="py-3 px-4">
                    <button type="button" class="text-gray-600 text-sm hover:text-black group" data-request="onEmptyCache" data-request-confirm="Weet je zeker dat je de cache wilt legen?" data-request-success="window.location.reload()">
                        <i class="text-base text-primary fa-solid fa-trash group-hover:text-primary-hover transition"></i>
                        <span class="pl-2 whitespace-nowrap overflow-hidden"
                            x-show="open"
                            x-cloak
                            x-transition:enter="transition-all duration-300 ease-out"
                            x-transition:enter-start="opacity-0 w-0"
                            x-transition:enter-end="opacity-100 w-full"
                            x-transition:leave="transition-all duration-150 ease-out"
                            x-transition:leave-start="opacity-100 w-full"
                            x-transition:leave-end="opacity-0 w-0">
                            Cache legen
                        </span>
                    </button>
                </div>
            {% endif %}
            {% if isSuperUser %}
                <div class="py-3 px-4">
                    <button type="button" class="text-gray-600 text-sm hover:text-black group" data-request="onMigrateDatabase" data-request-confirm="Weet je zeker dat je de database wilt migreren?" data-request-success="window.location.reload()">
                        <i class="text-base text-primary fa-solid fa-database group-hover:text-primary-hover transition"></i>
                        <span class="pl-2 whitespace-nowrap overflow-hidden"
                            x-show="open"
                            x-cloak
                            x-transition:enter="transition-all duration-300 ease-out"
                            x-transition:enter-start="opacity-0 w-0"
                            x-transition:enter-end="opacity-100 w-full"
                            x-transition:leave="transition-all duration-150 ease-out"
                            x-transition:leave-start="opacity-100 w-full"
                            x-transition:leave-end="opacity-0 w-0">
                            Migreer database
                        </span>
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}
