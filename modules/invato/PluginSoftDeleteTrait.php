<?php

namespace Invato;

use ApplicationException;
use Backend\Widgets\ListStructure;
use Illuminate\Database\Eloquent\Model;
use October\Rain\Support\Facades\Flash;
use RuntimeException;

trait PluginSoftDeleteTrait
{
    // START Soft Deletes
    /**
     * @throws ApplicationException
     */
    public function formFindModelObject($recordId): array|Model
    {
        $model = self::$modelClass::withTrashed()->find($recordId);

        if (! $model) {
            throw new ApplicationException(__('Form record with an ID of :id could not be found.', [
                'id' => $recordId,
            ]));
        }

        // Mimic parent method
        $this->formExtendModel($model);

        return $model;
    }

    public function onRestore()
    {
        if (! $id = post('id')) {
            throw new RuntimeException('ID not specified');
        }

        $model = self::$modelClass::withTrashed()->find($id);
        if (! $model) {
            throw new RuntimeException('Record not found');
        }

        if ($model->trashed()) {
            $this->restore($model->id);
            Flash::success(__('Restore successful'));
        } else {
            Flash::error(__('Restore failed'));
        }

        return $this->listRefresh();
    }

    public function onDelete()
    {
        $checked = post('checked');
        if ($checked && is_array($checked) && count($checked)) {
            foreach ($checked as $id) {
                $model = self::$modelClass::find($id);
                if ($model) {
                    $model->delete();
                }
            }

            Flash::success(__('Delete successful'));
            return $this->listRefresh();
        }

        Flash::error(__('No items selected'));
        return $this->listRefresh();
    }

    public function onRestoreSelected()
    {
        $checked = post('checked');
        if ($checked) {
            foreach ($checked as $id) {
                $this->restore($id);
            }

            Flash::success(__('Restore successful'));

            return $this->listRefresh();
        }

        return $this->listRefresh();
    }

    private function restore($id)
    {
        $model = self::$modelClass::withTrashed()->find($id);
        if (! $model) {
            throw new RuntimeException('Model not found');
        }

        if ($model->trashed()) {
            $model->deleted_at = null;
            $model->save();
        }
    }

    public function listExtendColumns($list): void
    {
        if ($list instanceof ListStructure) {
            $activeFilters = $this->getActiveFilters();
            if (isset($activeFilters['with_trashed']) && $activeFilters['with_trashed'] === '1') {
                $list->addColumns([
                    'deleted_at' => [
                        'label' => __('Deleted at'),
                        'type' => 'datetime',
                    ],
                ]);
            }
        }
    }

    public function getActiveFilters(): array
    {
        $filterWidget = $this->listGetFilterWidget();

        if ($filterWidget) {
            $scopes = $filterWidget->getScopes();

            $activeFilters = [];
            foreach ($scopes as $scopeName => $scope) {
                if ($scope->value) {
                    $activeFilters[$scopeName] = $scope->value;
                }
            }

            return $activeFilters;
        }

        return [];
    }
    // END Soft Deletes
}
