<?php

namespace Invato\Traits;

use ApplicationException;
use Cms\Classes\CmsException;
use Cms\Classes\Controller;
use Cms\Classes\Page as CmsPage;
use Cms\Classes\Theme;

trait HasPageFinderTrait
{
    /**
     * Get menu type information for PageFinder integration
     *
     * @throws ApplicationException
     */
    public static function getMenuTypeInfo(string $type): array
    {
        $config = static::getPageFinderConfig();

        $result = match ($type) {
            $config['single_type'] => static::buildSingleTypeInfo(),
            $config['all_type'] => static::buildAllTypeInfo(),
            default => [],
        };

        if (empty($result)) {
            return [];
        }

        $result['cmsPages'] = static::getCompatibleCmsPages($config['component']);

        return $result;
    }

    /**
     * Build menu type info for a single item selection
     */
    private static function buildSingleTypeInfo(): array
    {
        $references = [];
        $items = static::orderBy('title')->get();

        foreach ($items as $item) {
            $references[$item->id] = $item->title;
        }

        return [
            'references' => $references,
            'nesting' => false,
            'dynamicItems' => false,
        ];
    }

    /**
     * Build menu type info for all items
     */
    private static function buildAllTypeInfo(): array
    {
        return [
            'nesting' => true,
            'dynamicItems' => true,
        ];
    }

    /**
     * Get CMS pages that are compatible with the component
     *
     * @throws ApplicationException
     */
    private static function getCompatibleCmsPages(string $componentName): array
    {
        $theme = Theme::getActiveTheme();
        $pages = CmsPage::listInTheme($theme, true);
        $cmsPages = [];

        foreach ($pages as $page) {
            if (! $page->hasComponent($componentName)) {
                continue;
            }

            if (! static::hasValidSlugProperty($page, $componentName)) {
                continue;
            }

            $cmsPages[] = $page;
        }

        return $cmsPages;
    }

    /**
     * Check if page has valid slug property for dynamic routing
     */
    private static function hasValidSlugProperty(object $page, string $componentName): bool
    {
        $properties = $page->getComponentProperties($componentName);

        return preg_match('/{{\s*:/', $properties['slug'] ?? '') === 1;
    }

    /**
     * Resolve menu item for PageFinder integration
     *
     * @throws CmsException
     */
    public static function resolveMenuItem(object $item, string $url, object $theme): ?array
    {
        $config = static::getPageFinderConfig();

        return match ($item->type) {
            $config['single_type'] => static::resolveSingleMenuItem($item, $url, $theme),
            $config['all_type'] => static::resolveAllMenuItems($item, $url, $theme),
            default => null,
        };
    }

    /**
     * Resolve single menu item
     *
     * @throws CmsException
     */
    private static function resolveSingleMenuItem(object $item, string $url, object $theme): ?array
    {
        $model = static::find($item->reference);

        if (! $model) {
            return null;
        }

        $pageUrl = static::generatePageUrl($model, $item->cmsPage, $theme);

        return [
            'url' => $pageUrl,
            'isActive' => $pageUrl === $url,
            'title' => $model->title,
            'mtime' => $model->updated_at,
        ];
    }

    /**
     * Resolve all menu items
     *
     * @throws CmsException
     */
    private static function resolveAllMenuItems(object $item, string $url, object $theme): array
    {
        $result = ['items' => []];
        $models = static::orderBy('title')->get();

        foreach ($models as $model) {
            $result['items'][] = static::createMenuItemData($model, $item->cmsPage, $url, $theme);
        }

        return $result;
    }

    /**
     * Create menu item data for a model
     *
     * @throws CmsException
     */
    private static function createMenuItemData(object $model, $cmsPage, string $currentUrl, object $theme): array
    {
        $pageUrl = static::generatePageUrl($model, $cmsPage, $theme);

        return [
            'title' => $model->title,
            'url' => $pageUrl,
            'mtime' => $model->updated_at,
            'isActive' => $pageUrl === $currentUrl,
        ];
    }

    /**
     * Generate page URL for a model
     *
     * @throws CmsException
     */
    private static function generatePageUrl(object $model, $cmsPage, object $theme): string
    {
        return (new Controller($theme))->pageUrl($cmsPage, [
            'id' => $model->id,
            'slug' => $model->slug,
        ]);
    }

    /**
     * Get PageFinder configuration for this model
     * This method should be implemented by the model using this trait
     */
    abstract protected static function getPageFinderConfig(): array;
}
