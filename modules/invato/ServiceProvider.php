<?php

namespace Invato;

use App;
use Backend\Controllers\Users as BackendUsersController;
use Backend\Models\User as BackendUserModel;
use Cms\Classes\Controller;
use Detection\MobileDetect;
use Event;
use Invato\classes\InvatoAuthManager;
use Invato\Console\CreateChildTheme;
use Invato\controllers\InvatoController;
use Invato\Database\Seeders\DatabaseSeeder;
use October\Rain\Support\ModuleServiceProvider;

class ServiceProvider extends ModuleServiceProvider
{
    public function register(): void
    {
        parent::register('invato');

        $this->registerConsole();
        $this->registerConfig();

        $this->loadMigrationsFrom(__DIR__.'/database/migrations');
    }

    public function boot(): void
    {
        parent::boot('invato');

        // For maintenance mode changes
        App::bind(Controller::class, function () {
            return new InvatoController;
        });

        // Seeder logic
        if ($this->app->runningInConsole()) {
            $this->callAfterResolving('migrator', function ($migrator) {
                $seeder = new DatabaseSeeder;
                $seeder->run();
            });
        }

        // AuthManager logic
        App::extend('backend.auth', static function () {
            return InvatoAuthManager::instance();
        });

        BackendUsersController::extendFormFields(function ($form, $model, $context) {
            if (
                ($model instanceof BackendUserModel)
                &&
                $model->is_intranet_user
            ) {
                if ($context === 'myaccount') {
                    $form->removeField('password');
                    $form->removeField('password_confirmation');
                    $form->getField('login')->disabled();
                    $form->getField('email')->disabled();
                    $form->getField('first_name')->disabled();
                    $form->getField('last_name')->disabled();
                }

                if ($context === 'update') {
                    $form->removeField('password');
                    $form->removeField('password_confirmation');
                    $form->getField('login')->disabled();
                    $form->getField('email')->disabled();
                    $form->getField('first_name')->disabled();
                    $form->getField('last_name')->disabled();
                }
            }
        });

        BackendUserModel::extend(function (BackendUserModel $model) {
            $model->addFillable('is_intranet_user');
            $model->addFillable('is_active');
            $model->addFillable('access_to_websites');

            $model->addCasts([
                'is_intranet_user' => 'bool',
                'is_active' => 'bool',
                'access_to_websites' => 'array',
            ]);
        });

        // DeviceDetect
        Event::listen('cms.page.beforeDisplay', static function ($controller, $url, $page) {
            $detect = new MobileDetect;

            $controller->vars['isMobile'] = $detect->isMobile();
            $controller->vars['isTablet'] = $detect->isTablet();
            $controller->vars['isDesktop'] = ! $detect->isMobile() && ! $detect->isTablet();
        });
    }

    protected function registerConsole(): void
    {
        $this->registerConsoleCommand('invato.create-child-theme', CreateChildTheme::class);
    }

    protected function registerConfig(): void
    {
        $this->mergeConfigFrom(
            __DIR__.'/config/config.php', 'invato'
        );
    }
}
